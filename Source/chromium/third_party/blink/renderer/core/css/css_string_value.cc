// Copyright 2015 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include "third_party/blink/renderer/core/css/css_string_value.h"

#include "third_party/blink/renderer/core/css/css_markup.h"
#include "third_party/blink/renderer/platform/wtf/text/wtf_string.h"

namespace blink {

CSSStringValue::CSSStringValue(const String& str)
    : CSSValue(kStringClass), string_(str) {}

String CSSStringValue::CustomCSSText() const {
    return String(); // TODO:zjz
//  return SerializeString(string_);
}

void CSSStringValue::TraceAfterDispatch(blink::Visitor* visitor) const {
  CSSValue::TraceAfterDispatch(visitor);
}

}  // namespace blink
