// Copyright 2016 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// This header is hand-written, whereas most CSSProperty subclass
// headers are generated by core/css/properties/templates/Subclass.h.tmpl.
// CSSPropertyID::kVariable is treated as a CSS properties in some places for
// convenience but is not really a CSS property.
#ifndef THIRD_PARTY_BLINK_RENDERER_CORE_CSS_PROPERTIES_LONGHANDS_VARIABLE_H_
#define THIRD_PARTY_BLINK_RENDERER_CORE_CSS_PROPERTIES_LONGHANDS_VARIABLE_H_

#include "base/notreached.h"
#include "third_party/blink/renderer/core/core_export.h"
#include "third_party/blink/renderer/core/css/properties/longhand.h"

namespace blink {

// TODO(https://crbug.com/980160): Remove this class when the static Variable
// instance (as returned by GetCSSPropertyVariable()) has been removed.
class CORE_EXPORT Variable : public Longhand {
 public:
  constexpr Variable() : Variable(true) {}

  bool IsAffectedByAll() const override { return false; }
  CSSPropertyName GetCSSPropertyName() const override {
    NOTREACHED();
    return CSSPropertyName(g_empty_atom);
  }
  const char* GetPropertyName() const override { return "variable"; }
  const WTF::AtomicString& GetPropertyNameAtomicString() const override {
    DEFINE_STATIC_LOCAL(const AtomicString, name, ("variable"));
    return name;
  }

  static bool IsStaticInstance(const CSSProperty&);

 protected:
  explicit constexpr Variable(CSSProperty::Flags flags)
      : Longhand(CSSPropertyID::kVariable,
                 kProperty | kValidForFirstLetter | kValidForFirstLine |
                     kValidForMarker | kValidForHighlightLegacy |
                     kValidForHighlight | flags,
                 '\0') {}
};

}  // namespace blink

#endif  // THIRD_PARTY_BLINK_RENDERER_CORE_CSS_PROPERTIES_LONGHANDS_VARIABLE_H_
