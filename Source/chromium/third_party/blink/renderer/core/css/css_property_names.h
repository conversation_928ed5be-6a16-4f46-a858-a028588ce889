// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef THIRD_PARTY_BLINK_RENDERER_CORE_CSS_CSS_PROPERTY_NAMES_H_
#define THIRD_PARTY_BLINK_RENDERER_CORE_CSS_CSS_PROPERTY_NAMES_H_

#include <stddef.h>

#include "base/check_op.h"
#include "third_party/blink/public/mojom/use_counter/metrics/css_property_id.mojom-blink-forward.h"
#include "third_party/blink/renderer/core/core_export.h"
#include "third_party/blink/renderer/platform/wtf/allocator/allocator.h"
#include "third_party/blink/renderer/platform/wtf/hash_functions.h"
#include "third_party/blink/renderer/platform/wtf/hash_traits.h"

namespace WTF {
class AtomicString;
class String;
}  // namespace WTF

namespace blink {

class ExecutionContext;

enum class CSSPropertyID {
    kInvalid = 0,
    kVariable = 1,
    kColorScheme = 2,
    kForcedColorAdjust = 3,
    kMaskImage = 4,
    kMathDepth = 5,
    kPosition = 6,
    kPositionAnchor = 7,
    kAppearance = 8,
    kColor = 9,
    kDirection = 10,
    kFontFamily = 11,
    kFontFeatureSettings = 12,
    kFontKerning = 13,
    kFontOpticalSizing = 14,
    kFontPalette = 15,
    kFontSize = 16,
    kFontSizeAdjust = 17,
    kFontStretch = 18,
    kFontStyle = 19,
    kFontSynthesisSmallCaps = 20,
    kFontSynthesisStyle = 21,
    kFontSynthesisWeight = 22,
    kFontVariantAlternates = 23,
    kFontVariantCaps = 24,
    kFontVariantEastAsian = 25,
    kFontVariantEmoji = 26,
    kFontVariantLigatures = 27,
    kFontVariantNumeric = 28,
    kFontVariantPosition = 29,
    kFontVariationSettings = 30,
    kFontWeight = 31,
    kInsetArea = 32,
    kInternalVisitedColor = 33,
    kTextOrientation = 34,
    kTextRendering = 35,
    kTextSpacingTrim = 36,
    kWebkitFontSmoothing = 37,
    kWebkitLocale = 38,
    kWebkitTextOrientation = 39,
    kWebkitWritingMode = 40,
    kWritingMode = 41,
    kZoom = 42,
    kAccentColor = 43,
    kAdditiveSymbols = 44,
    kAlignContent = 45,
    kAlignItems = 46,
    kAlignSelf = 47,
    kAlignmentBaseline = 48,
    kAll = 49,
    kAnchorName = 50,
    kAnimationComposition = 51,
    kAnimationDelay = 52,
    kAnimationDirection = 53,
    kAnimationDuration = 54,
    kAnimationFillMode = 55,
    kAnimationIterationCount = 56,
    kAnimationName = 57,
    kAnimationPlayState = 58,
    kAnimationRangeEnd = 59,
    kAnimationRangeStart = 60,
    kAnimationTimeline = 61,
    kAnimationTimingFunction = 62,
    kAppRegion = 63,
    kAscentOverride = 64,
    kAspectRatio = 65,
    kBackdropFilter = 66,
    kBackfaceVisibility = 67,
    kBackgroundAttachment = 68,
    kBackgroundBlendMode = 69,
    kBackgroundClip = 70,
    kBackgroundColor = 71,
    kBackgroundImage = 72,
    kBackgroundOrigin = 73,
    kBackgroundPositionX = 74,
    kBackgroundPositionY = 75,
    kBackgroundRepeat = 76,
    kBackgroundSize = 77,
    kBasePalette = 78,
    kBaselineShift = 79,
    kBaselineSource = 80,
    kBlockSize = 81,
    kBorderBlockEndColor = 82,
    kBorderBlockEndStyle = 83,
    kBorderBlockEndWidth = 84,
    kBorderBlockStartColor = 85,
    kBorderBlockStartStyle = 86,
    kBorderBlockStartWidth = 87,
    kBorderBottomColor = 88,
    kBorderBottomLeftRadius = 89,
    kBorderBottomRightRadius = 90,
    kBorderBottomStyle = 91,
    kBorderBottomWidth = 92,
    kBorderCollapse = 93,
    kBorderEndEndRadius = 94,
    kBorderEndStartRadius = 95,
    kBorderImageOutset = 96,
    kBorderImageRepeat = 97,
    kBorderImageSlice = 98,
    kBorderImageSource = 99,
    kBorderImageWidth = 100,
    kBorderInlineEndColor = 101,
    kBorderInlineEndStyle = 102,
    kBorderInlineEndWidth = 103,
    kBorderInlineStartColor = 104,
    kBorderInlineStartStyle = 105,
    kBorderInlineStartWidth = 106,
    kBorderLeftColor = 107,
    kBorderLeftStyle = 108,
    kBorderLeftWidth = 109,
    kBorderRightColor = 110,
    kBorderRightStyle = 111,
    kBorderRightWidth = 112,
    kBorderStartEndRadius = 113,
    kBorderStartStartRadius = 114,
    kBorderTopColor = 115,
    kBorderTopLeftRadius = 116,
    kBorderTopRightRadius = 117,
    kBorderTopStyle = 118,
    kBorderTopWidth = 119,
    kBottom = 120,
    kBoxShadow = 121,
    kBoxSizing = 122,
    kBreakAfter = 123,
    kBreakBefore = 124,
    kBreakInside = 125,
    kBufferedRendering = 126,
    kCaptionSide = 127,
    kCaretColor = 128,
    kClear = 129,
    kClip = 130,
    kClipPath = 131,
    kClipRule = 132,
    kColorInterpolation = 133,
    kColorInterpolationFilters = 134,
    kColorRendering = 135,
    kColumnCount = 136,
    kColumnFill = 137,
    kColumnGap = 138,
    kColumnRuleColor = 139,
    kColumnRuleStyle = 140,
    kColumnRuleWidth = 141,
    kColumnSpan = 142,
    kColumnWidth = 143,
    kContain = 144,
    kContainIntrinsicBlockSize = 145,
    kContainIntrinsicHeight = 146,
    kContainIntrinsicInlineSize = 147,
    kContainIntrinsicWidth = 148,
    kContainerName = 149,
    kContainerType = 150,
    kContent = 151,
    kContentVisibility = 152,
    kCounterIncrement = 153,
    kCounterReset = 154,
    kCounterSet = 155,
    kCursor = 156,
    kCx = 157,
    kCy = 158,
    kD = 159,
    kDescentOverride = 160,
    kDisplay = 161,
    kDominantBaseline = 162,
    kDynamicRangeLimit = 163,
    kEmptyCells = 164,
    kFallback = 165,
    kFieldSizing = 166,
    kFill = 167,
    kFillOpacity = 168,
    kFillRule = 169,
    kFilter = 170,
    kFlexBasis = 171,
    kFlexDirection = 172,
    kFlexGrow = 173,
    kFlexShrink = 174,
    kFlexWrap = 175,
    kFloat = 176,
    kFloodColor = 177,
    kFloodOpacity = 178,
    kFontDisplay = 179,
    kGridAutoColumns = 180,
    kGridAutoFlow = 181,
    kGridAutoRows = 182,
    kGridColumnEnd = 183,
    kGridColumnStart = 184,
    kGridRowEnd = 185,
    kGridRowStart = 186,
    kGridTemplateAreas = 187,
    kGridTemplateColumns = 188,
    kGridTemplateRows = 189,
    kHeight = 190,
    kHyphenateCharacter = 191,
    kHyphenateLimitChars = 192,
    kHyphens = 193,
    kImageOrientation = 194,
    kImageRendering = 195,
    kInherits = 196,
    kInitialLetter = 197,
    kInitialValue = 198,
    kInlineSize = 199,
    kInsetBlockEnd = 200,
    kInsetBlockStart = 201,
    kInsetInlineEnd = 202,
    kInsetInlineStart = 203,
    kInternalAlignContentBlock = 204,
    kInternalEmptyLineHeight = 205,
    kInternalFontSizeDelta = 206,
    kInternalForcedBackgroundColor = 207,
    kInternalForcedBorderColor = 208,
    kInternalForcedColor = 209,
    kInternalForcedOutlineColor = 210,
    kInternalForcedVisitedColor = 211,
    kInternalOverflowBlock = 212,
    kInternalOverflowInline = 213,
    kInternalVisitedBackgroundColor = 214,
    kInternalVisitedBorderBlockEndColor = 215,
    kInternalVisitedBorderBlockStartColor = 216,
    kInternalVisitedBorderBottomColor = 217,
    kInternalVisitedBorderInlineEndColor = 218,
    kInternalVisitedBorderInlineStartColor = 219,
    kInternalVisitedBorderLeftColor = 220,
    kInternalVisitedBorderRightColor = 221,
    kInternalVisitedBorderTopColor = 222,
    kInternalVisitedCaretColor = 223,
    kInternalVisitedColumnRuleColor = 224,
    kInternalVisitedFill = 225,
    kInternalVisitedOutlineColor = 226,
    kInternalVisitedStroke = 227,
    kInternalVisitedTextDecorationColor = 228,
    kInternalVisitedTextEmphasisColor = 229,
    kInternalVisitedTextFillColor = 230,
    kInternalVisitedTextStrokeColor = 231,
    kIsolation = 232,
    kJustifyContent = 233,
    kJustifyItems = 234,
    kJustifySelf = 235,
    kLeft = 236,
    kLetterSpacing = 237,
    kLightingColor = 238,
    kLineBreak = 239,
    kLineClamp = 240,
    kLineGapOverride = 241,
    kLineHeight = 242,
    kListStyleImage = 243,
    kListStylePosition = 244,
    kListStyleType = 245,
    kMarginBlockEnd = 246,
    kMarginBlockStart = 247,
    kMarginBottom = 248,
    kMarginInlineEnd = 249,
    kMarginInlineStart = 250,
    kMarginLeft = 251,
    kMarginRight = 252,
    kMarginTop = 253,
    kMarkerEnd = 254,
    kMarkerMid = 255,
    kMarkerStart = 256,
    kMaskClip = 257,
    kMaskComposite = 258,
    kMaskMode = 259,
    kMaskOrigin = 260,
    kMaskRepeat = 261,
    kMaskSize = 262,
    kMaskType = 263,
    kMathShift = 264,
    kMathStyle = 265,
    kMaxBlockSize = 266,
    kMaxHeight = 267,
    kMaxInlineSize = 268,
    kMaxWidth = 269,
    kMinBlockSize = 270,
    kMinHeight = 271,
    kMinInlineSize = 272,
    kMinWidth = 273,
    kMixBlendMode = 274,
    kNavigation = 275,
    kNegative = 276,
    kObjectFit = 277,
    kObjectPosition = 278,
    kObjectViewBox = 279,
    kOffsetAnchor = 280,
    kOffsetDistance = 281,
    kOffsetPath = 282,
    kOffsetPosition = 283,
    kOffsetRotate = 284,
    kOpacity = 285,
    kOrder = 286,
    kOriginTrialTestProperty = 287,
    kOrphans = 288,
    kOutlineColor = 289,
    kOutlineOffset = 290,
    kOutlineStyle = 291,
    kOutlineWidth = 292,
    kOverflowAnchor = 293,
    kOverflowBlock = 294,
    kOverflowClipMargin = 295,
    kOverflowInline = 296,
    kOverflowWrap = 297,
    kOverflowX = 298,
    kOverflowY = 299,
    kOverlay = 300,
    kOverrideColors = 301,
    kOverscrollBehaviorBlock = 302,
    kOverscrollBehaviorInline = 303,
    kOverscrollBehaviorX = 304,
    kOverscrollBehaviorY = 305,
    kPad = 306,
    kPaddingBlockEnd = 307,
    kPaddingBlockStart = 308,
    kPaddingBottom = 309,
    kPaddingInlineEnd = 310,
    kPaddingInlineStart = 311,
    kPaddingLeft = 312,
    kPaddingRight = 313,
    kPaddingTop = 314,
    kPage = 315,
    kPageOrientation = 316,
    kPaintOrder = 317,
    kPerspective = 318,
    kPerspectiveOrigin = 319,
    kPointerEvents = 320,
    kPopoverHideDelay = 321,
    kPopoverShowDelay = 322,
    kPositionTryOptions = 323,
    kPositionTryOrder = 324,
    kPositionVisibility = 325,
    kPrefix = 326,
    kQuotes = 327,
    kR = 328,
    kRange = 329,
    kReadingOrderItems = 330,
    kResize = 331,
    kRight = 332,
    kRotate = 333,
    kRowGap = 334,
    kRubyPosition = 335,
    kRx = 336,
    kRy = 337,
    kScale = 338,
    kScrollBehavior = 339,
    kScrollMarginBlockEnd = 340,
    kScrollMarginBlockStart = 341,
    kScrollMarginBottom = 342,
    kScrollMarginInlineEnd = 343,
    kScrollMarginInlineStart = 344,
    kScrollMarginLeft = 345,
    kScrollMarginRight = 346,
    kScrollMarginTop = 347,
    kScrollPaddingBlockEnd = 348,
    kScrollPaddingBlockStart = 349,
    kScrollPaddingBottom = 350,
    kScrollPaddingInlineEnd = 351,
    kScrollPaddingInlineStart = 352,
    kScrollPaddingLeft = 353,
    kScrollPaddingRight = 354,
    kScrollPaddingTop = 355,
    kScrollSnapAlign = 356,
    kScrollSnapStop = 357,
    kScrollSnapType = 358,
    kScrollStartBlock = 359,
    kScrollStartInline = 360,
    kScrollStartTargetBlock = 361,
    kScrollStartTargetInline = 362,
    kScrollStartTargetX = 363,
    kScrollStartTargetY = 364,
    kScrollStartX = 365,
    kScrollStartY = 366,
    kScrollTimelineAxis = 367,
    kScrollTimelineName = 368,
    kScrollbarColor = 369,
    kScrollbarGutter = 370,
    kScrollbarWidth = 371,
    kShapeImageThreshold = 372,
    kShapeMargin = 373,
    kShapeOutside = 374,
    kShapeRendering = 375,
    kSize = 376,
    kSizeAdjust = 377,
    kSpeak = 378,
    kSpeakAs = 379,
    kSrc = 380,
    kStopColor = 381,
    kStopOpacity = 382,
    kStroke = 383,
    kStrokeDasharray = 384,
    kStrokeDashoffset = 385,
    kStrokeLinecap = 386,
    kStrokeLinejoin = 387,
    kStrokeMiterlimit = 388,
    kStrokeOpacity = 389,
    kStrokeWidth = 390,
    kSuffix = 391,
    kSymbols = 392,
    kSyntax = 393,
    kSystem = 394,
    kTabSize = 395,
    kTableLayout = 396,
    kTextAlign = 397,
    kTextAlignLast = 398,
    kTextAnchor = 399,
    kTextAutospace = 400,
    kTextBoxEdge = 401,
    kTextBoxTrim = 402,
    kTextCombineUpright = 403,
    kTextDecorationColor = 404,
    kTextDecorationLine = 405,
    kTextDecorationSkipInk = 406,
    kTextDecorationStyle = 407,
    kTextDecorationThickness = 408,
    kTextEmphasisColor = 409,
    kTextEmphasisPosition = 410,
    kTextEmphasisStyle = 411,
    kTextIndent = 412,
    kTextOverflow = 413,
    kTextShadow = 414,
    kTextSizeAdjust = 415,
    kTextTransform = 416,
    kTextUnderlineOffset = 417,
    kTextUnderlinePosition = 418,
    kTextWrap = 419,
    kTimelineScope = 420,
    kTop = 421,
    kTouchAction = 422,
    kTransform = 423,
    kTransformBox = 424,
    kTransformOrigin = 425,
    kTransformStyle = 426,
    kTransitionBehavior = 427,
    kTransitionDelay = 428,
    kTransitionDuration = 429,
    kTransitionProperty = 430,
    kTransitionTimingFunction = 431,
    kTranslate = 432,
    kTypes = 433,
    kUnicodeBidi = 434,
    kUnicodeRange = 435,
    kUserSelect = 436,
    kVectorEffect = 437,
    kVerticalAlign = 438,
    kViewTimelineAxis = 439,
    kViewTimelineInset = 440,
    kViewTimelineName = 441,
    kViewTransitionClass = 442,
    kViewTransitionName = 443,
    kVisibility = 444,
    kWebkitBorderHorizontalSpacing = 445,
    kWebkitBorderImage = 446,
    kWebkitBorderVerticalSpacing = 447,
    kWebkitBoxAlign = 448,
    kWebkitBoxDecorationBreak = 449,
    kWebkitBoxDirection = 450,
    kWebkitBoxFlex = 451,
    kWebkitBoxOrdinalGroup = 452,
    kWebkitBoxOrient = 453,
    kWebkitBoxPack = 454,
    kWebkitBoxReflect = 455,
    kWebkitLineBreak = 456,
    kWebkitLineClamp = 457,
    kWebkitMaskBoxImageOutset = 458,
    kWebkitMaskBoxImageRepeat = 459,
    kWebkitMaskBoxImageSlice = 460,
    kWebkitMaskBoxImageSource = 461,
    kWebkitMaskBoxImageWidth = 462,
    kWebkitMaskPositionX = 463,
    kWebkitMaskPositionY = 464,
    kWebkitPerspectiveOriginX = 465,
    kWebkitPerspectiveOriginY = 466,
    kWebkitPrintColorAdjust = 467,
    kWebkitRtlOrdering = 468,
    kWebkitRubyPosition = 469,
    kWebkitTapHighlightColor = 470,
    kWebkitTextCombine = 471,
    kWebkitTextDecorationsInEffect = 472,
    kWebkitTextFillColor = 473,
    kWebkitTextSecurity = 474,
    kWebkitTextStrokeColor = 475,
    kWebkitTextStrokeWidth = 476,
    kWebkitTransformOriginX = 477,
    kWebkitTransformOriginY = 478,
    kWebkitTransformOriginZ = 479,
    kWebkitUserDrag = 480,
    kWebkitUserModify = 481,
    kWhiteSpaceCollapse = 482,
    kWidows = 483,
    kWidth = 484,
    kWillChange = 485,
    kWordBreak = 486,
    kWordSpacing = 487,
    kX = 488,
    kY = 489,
    kZIndex = 490,
    kAlternativeAnimationWithTimeline = 491,
    kAnimation = 492,
    kAnimationRange = 493,
    kBackground = 494,
    kBackgroundPosition = 495,
    kBorder = 496,
    kBorderBlock = 497,
    kBorderBlockColor = 498,
    kBorderBlockEnd = 499,
    kBorderBlockStart = 500,
    kBorderBlockStyle = 501,
    kBorderBlockWidth = 502,
    kBorderBottom = 503,
    kBorderColor = 504,
    kBorderImage = 505,
    kBorderInline = 506,
    kBorderInlineColor = 507,
    kBorderInlineEnd = 508,
    kBorderInlineStart = 509,
    kBorderInlineStyle = 510,
    kBorderInlineWidth = 511,
    kBorderLeft = 512,
    kBorderRadius = 513,
    kBorderRight = 514,
    kBorderSpacing = 515,
    kBorderStyle = 516,
    kBorderTop = 517,
    kBorderWidth = 518,
    kColumnRule = 519,
    kColumns = 520,
    kContainIntrinsicSize = 521,
    kContainer = 522,
    kFlex = 523,
    kFlexFlow = 524,
    kFont = 525,
    kFontSynthesis = 526,
    kFontVariant = 527,
    kGap = 528,
    kGrid = 529,
    kGridArea = 530,
    kGridColumn = 531,
    kGridRow = 532,
    kGridTemplate = 533,
    kInset = 534,
    kInsetBlock = 535,
    kInsetInline = 536,
    kListStyle = 537,
    kMargin = 538,
    kMarginBlock = 539,
    kMarginInline = 540,
    kMarker = 541,
    kMask = 542,
    kMaskPosition = 543,
    kOffset = 544,
    kOutline = 545,
    kOverflow = 546,
    kOverscrollBehavior = 547,
    kPadding = 548,
    kPaddingBlock = 549,
    kPaddingInline = 550,
    kPageBreakAfter = 551,
    kPageBreakBefore = 552,
    kPageBreakInside = 553,
    kPlaceContent = 554,
    kPlaceItems = 555,
    kPlaceSelf = 556,
    kPositionTry = 557,
    kScrollMargin = 558,
    kScrollMarginBlock = 559,
    kScrollMarginInline = 560,
    kScrollPadding = 561,
    kScrollPaddingBlock = 562,
    kScrollPaddingInline = 563,
    kScrollStart = 564,
    kScrollStartTarget = 565,
    kScrollTimeline = 566,
    kTextDecoration = 567,
    kTextEmphasis = 568,
    kTextSpacing = 569,
    kTransition = 570,
    kViewTimeline = 571,
    kWebkitColumnBreakAfter = 572,
    kWebkitColumnBreakBefore = 573,
    kWebkitColumnBreakInside = 574,
    kWebkitMaskBoxImage = 575,
    kWebkitTextStroke = 576,
    kWhiteSpace = 577,
    kAliasWebkitAppearance = 578,
    kAliasWebkitAppRegion = 579,
    kAliasWebkitMaskClip = 580,
    kAliasWebkitMaskComposite = 581,
    kAliasWebkitMaskImage = 582,
    kAliasWebkitMaskOrigin = 583,
    kAliasWebkitMaskRepeat = 584,
    kAliasWebkitMaskSize = 585,
    kAliasWebkitBorderEndColor = 586,
    kAliasWebkitBorderEndStyle = 587,
    kAliasWebkitBorderEndWidth = 588,
    kAliasWebkitBorderStartColor = 589,
    kAliasWebkitBorderStartStyle = 590,
    kAliasWebkitBorderStartWidth = 591,
    kAliasWebkitBorderBeforeColor = 592,
    kAliasWebkitBorderBeforeStyle = 593,
    kAliasWebkitBorderBeforeWidth = 594,
    kAliasWebkitBorderAfterColor = 595,
    kAliasWebkitBorderAfterStyle = 596,
    kAliasWebkitBorderAfterWidth = 597,
    kAliasWebkitMarginEnd = 598,
    kAliasWebkitMarginStart = 599,
    kAliasWebkitMarginBefore = 600,
    kAliasWebkitMarginAfter = 601,
    kAliasWebkitPaddingEnd = 602,
    kAliasWebkitPaddingStart = 603,
    kAliasWebkitPaddingBefore = 604,
    kAliasWebkitPaddingAfter = 605,
    kAliasWebkitLogicalWidth = 606,
    kAliasWebkitLogicalHeight = 607,
    kAliasWebkitMinLogicalWidth = 608,
    kAliasWebkitMinLogicalHeight = 609,
    kAliasWebkitMaxLogicalWidth = 610,
    kAliasWebkitMaxLogicalHeight = 611,
    kAliasWebkitBorderAfter = 612,
    kAliasWebkitBorderBefore = 613,
    kAliasWebkitBorderEnd = 614,
    kAliasWebkitBorderStart = 615,
    kAliasWebkitMask = 616,
    kAliasWebkitMaskPosition = 617,
    kAliasEpubCaptionSide = 618,
    kAliasEpubTextCombine = 619,
    kAliasEpubTextEmphasis = 620,
    kAliasEpubTextEmphasisColor = 621,
    kAliasEpubTextEmphasisStyle = 622,
    kAliasEpubTextOrientation = 623,
    kAliasEpubTextTransform = 624,
    kAliasEpubWordBreak = 625,
    kAliasEpubWritingMode = 626,
    kAliasWebkitAlignContent = 627,
    kAliasWebkitAlignItems = 628,
    kAliasWebkitAlignSelf = 629,
    kAliasWebkitAnimation = 630,
    kAliasWebkitAlternativeAnimationWithTimeline = 631,
    kAliasWebkitAnimationDelay = 632,
    kAliasWebkitAnimationDirection = 633,
    kAliasWebkitAnimationDuration = 634,
    kAliasWebkitAnimationFillMode = 635,
    kAliasWebkitAnimationIterationCount = 636,
    kAliasWebkitAnimationName = 637,
    kAliasWebkitAnimationPlayState = 638,
    kAliasWebkitAnimationTimingFunction = 639,
    kAliasWebkitBackfaceVisibility = 640,
    kAliasWebkitBackgroundClip = 641,
    kAliasWebkitBackgroundOrigin = 642,
    kAliasWebkitBackgroundSize = 643,
    kAliasWebkitBorderBottomLeftRadius = 644,
    kAliasWebkitBorderBottomRightRadius = 645,
    kAliasWebkitBorderRadius = 646,
    kAliasWebkitBorderTopLeftRadius = 647,
    kAliasWebkitBorderTopRightRadius = 648,
    kAliasWebkitBoxShadow = 649,
    kAliasWebkitBoxSizing = 650,
    kAliasWebkitClipPath = 651,
    kAliasWebkitColumnCount = 652,
    kAliasWebkitColumnGap = 653,
    kAliasWebkitColumnRule = 654,
    kAliasWebkitColumnRuleColor = 655,
    kAliasWebkitColumnRuleStyle = 656,
    kAliasWebkitColumnRuleWidth = 657,
    kAliasWebkitColumnSpan = 658,
    kAliasWebkitColumnWidth = 659,
    kAliasWebkitColumns = 660,
    kAliasWebkitFilter = 661,
    kAliasWebkitFlex = 662,
    kAliasWebkitFlexBasis = 663,
    kAliasWebkitFlexDirection = 664,
    kAliasWebkitFlexFlow = 665,
    kAliasWebkitFlexGrow = 666,
    kAliasWebkitFlexShrink = 667,
    kAliasWebkitFlexWrap = 668,
    kAliasWebkitFontFeatureSettings = 669,
    kAliasWebkitHyphenateCharacter = 670,
    kAliasWebkitJustifyContent = 671,
    kAliasWebkitOpacity = 672,
    kAliasWebkitOrder = 673,
    kAliasWebkitPerspective = 674,
    kAliasWebkitPerspectiveOrigin = 675,
    kAliasWebkitShapeImageThreshold = 676,
    kAliasWebkitShapeMargin = 677,
    kAliasWebkitShapeOutside = 678,
    kAliasWebkitTextEmphasis = 679,
    kAliasWebkitTextEmphasisColor = 680,
    kAliasWebkitTextEmphasisPosition = 681,
    kAliasWebkitTextEmphasisStyle = 682,
    kAliasWebkitTextSizeAdjust = 683,
    kAliasWebkitTransform = 684,
    kAliasWebkitTransformOrigin = 685,
    kAliasWebkitTransformStyle = 686,
    kAliasWebkitTransition = 687,
    kAliasWebkitTransitionDelay = 688,
    kAliasWebkitTransitionDuration = 689,
    kAliasWebkitTransitionProperty = 690,
    kAliasWebkitTransitionTimingFunction = 691,
    kAliasWebkitUserSelect = 692,
    kAliasWordWrap = 693,
    kAliasGridColumnGap = 694,
    kAliasGridRowGap = 695,
    kAliasGridGap = 696,
};

const CSSPropertyID kCSSPropertyAliasList[] = {
    CSSPropertyID::kAliasWebkitAppearance,
    CSSPropertyID::kAliasWebkitAppRegion,
    CSSPropertyID::kAliasWebkitMaskClip,
    CSSPropertyID::kAliasWebkitMaskComposite,
    CSSPropertyID::kAliasWebkitMaskImage,
    CSSPropertyID::kAliasWebkitMaskOrigin,
    CSSPropertyID::kAliasWebkitMaskRepeat,
    CSSPropertyID::kAliasWebkitMaskSize,
    CSSPropertyID::kAliasWebkitBorderEndColor,
    CSSPropertyID::kAliasWebkitBorderEndStyle,
    CSSPropertyID::kAliasWebkitBorderEndWidth,
    CSSPropertyID::kAliasWebkitBorderStartColor,
    CSSPropertyID::kAliasWebkitBorderStartStyle,
    CSSPropertyID::kAliasWebkitBorderStartWidth,
    CSSPropertyID::kAliasWebkitBorderBeforeColor,
    CSSPropertyID::kAliasWebkitBorderBeforeStyle,
    CSSPropertyID::kAliasWebkitBorderBeforeWidth,
    CSSPropertyID::kAliasWebkitBorderAfterColor,
    CSSPropertyID::kAliasWebkitBorderAfterStyle,
    CSSPropertyID::kAliasWebkitBorderAfterWidth,
    CSSPropertyID::kAliasWebkitMarginEnd,
    CSSPropertyID::kAliasWebkitMarginStart,
    CSSPropertyID::kAliasWebkitMarginBefore,
    CSSPropertyID::kAliasWebkitMarginAfter,
    CSSPropertyID::kAliasWebkitPaddingEnd,
    CSSPropertyID::kAliasWebkitPaddingStart,
    CSSPropertyID::kAliasWebkitPaddingBefore,
    CSSPropertyID::kAliasWebkitPaddingAfter,
    CSSPropertyID::kAliasWebkitLogicalWidth,
    CSSPropertyID::kAliasWebkitLogicalHeight,
    CSSPropertyID::kAliasWebkitMinLogicalWidth,
    CSSPropertyID::kAliasWebkitMinLogicalHeight,
    CSSPropertyID::kAliasWebkitMaxLogicalWidth,
    CSSPropertyID::kAliasWebkitMaxLogicalHeight,
    CSSPropertyID::kAliasWebkitBorderAfter,
    CSSPropertyID::kAliasWebkitBorderBefore,
    CSSPropertyID::kAliasWebkitBorderEnd,
    CSSPropertyID::kAliasWebkitBorderStart,
    CSSPropertyID::kAliasWebkitMask,
    CSSPropertyID::kAliasWebkitMaskPosition,
    CSSPropertyID::kAliasEpubCaptionSide,
    CSSPropertyID::kAliasEpubTextCombine,
    CSSPropertyID::kAliasEpubTextEmphasis,
    CSSPropertyID::kAliasEpubTextEmphasisColor,
    CSSPropertyID::kAliasEpubTextEmphasisStyle,
    CSSPropertyID::kAliasEpubTextOrientation,
    CSSPropertyID::kAliasEpubTextTransform,
    CSSPropertyID::kAliasEpubWordBreak,
    CSSPropertyID::kAliasEpubWritingMode,
    CSSPropertyID::kAliasWebkitAlignContent,
    CSSPropertyID::kAliasWebkitAlignItems,
    CSSPropertyID::kAliasWebkitAlignSelf,
    CSSPropertyID::kAliasWebkitAnimation,
    CSSPropertyID::kAliasWebkitAlternativeAnimationWithTimeline,
    CSSPropertyID::kAliasWebkitAnimationDelay,
    CSSPropertyID::kAliasWebkitAnimationDirection,
    CSSPropertyID::kAliasWebkitAnimationDuration,
    CSSPropertyID::kAliasWebkitAnimationFillMode,
    CSSPropertyID::kAliasWebkitAnimationIterationCount,
    CSSPropertyID::kAliasWebkitAnimationName,
    CSSPropertyID::kAliasWebkitAnimationPlayState,
    CSSPropertyID::kAliasWebkitAnimationTimingFunction,
    CSSPropertyID::kAliasWebkitBackfaceVisibility,
    CSSPropertyID::kAliasWebkitBackgroundClip,
    CSSPropertyID::kAliasWebkitBackgroundOrigin,
    CSSPropertyID::kAliasWebkitBackgroundSize,
    CSSPropertyID::kAliasWebkitBorderBottomLeftRadius,
    CSSPropertyID::kAliasWebkitBorderBottomRightRadius,
    CSSPropertyID::kAliasWebkitBorderRadius,
    CSSPropertyID::kAliasWebkitBorderTopLeftRadius,
    CSSPropertyID::kAliasWebkitBorderTopRightRadius,
    CSSPropertyID::kAliasWebkitBoxShadow,
    CSSPropertyID::kAliasWebkitBoxSizing,
    CSSPropertyID::kAliasWebkitClipPath,
    CSSPropertyID::kAliasWebkitColumnCount,
    CSSPropertyID::kAliasWebkitColumnGap,
    CSSPropertyID::kAliasWebkitColumnRule,
    CSSPropertyID::kAliasWebkitColumnRuleColor,
    CSSPropertyID::kAliasWebkitColumnRuleStyle,
    CSSPropertyID::kAliasWebkitColumnRuleWidth,
    CSSPropertyID::kAliasWebkitColumnSpan,
    CSSPropertyID::kAliasWebkitColumnWidth,
    CSSPropertyID::kAliasWebkitColumns,
    CSSPropertyID::kAliasWebkitFilter,
    CSSPropertyID::kAliasWebkitFlex,
    CSSPropertyID::kAliasWebkitFlexBasis,
    CSSPropertyID::kAliasWebkitFlexDirection,
    CSSPropertyID::kAliasWebkitFlexFlow,
    CSSPropertyID::kAliasWebkitFlexGrow,
    CSSPropertyID::kAliasWebkitFlexShrink,
    CSSPropertyID::kAliasWebkitFlexWrap,
    CSSPropertyID::kAliasWebkitFontFeatureSettings,
    CSSPropertyID::kAliasWebkitHyphenateCharacter,
    CSSPropertyID::kAliasWebkitJustifyContent,
    CSSPropertyID::kAliasWebkitOpacity,
    CSSPropertyID::kAliasWebkitOrder,
    CSSPropertyID::kAliasWebkitPerspective,
    CSSPropertyID::kAliasWebkitPerspectiveOrigin,
    CSSPropertyID::kAliasWebkitShapeImageThreshold,
    CSSPropertyID::kAliasWebkitShapeMargin,
    CSSPropertyID::kAliasWebkitShapeOutside,
    CSSPropertyID::kAliasWebkitTextEmphasis,
    CSSPropertyID::kAliasWebkitTextEmphasisColor,
    CSSPropertyID::kAliasWebkitTextEmphasisPosition,
    CSSPropertyID::kAliasWebkitTextEmphasisStyle,
    CSSPropertyID::kAliasWebkitTextSizeAdjust,
    CSSPropertyID::kAliasWebkitTransform,
    CSSPropertyID::kAliasWebkitTransformOrigin,
    CSSPropertyID::kAliasWebkitTransformStyle,
    CSSPropertyID::kAliasWebkitTransition,
    CSSPropertyID::kAliasWebkitTransitionDelay,
    CSSPropertyID::kAliasWebkitTransitionDuration,
    CSSPropertyID::kAliasWebkitTransitionProperty,
    CSSPropertyID::kAliasWebkitTransitionTimingFunction,
    CSSPropertyID::kAliasWebkitUserSelect,
    CSSPropertyID::kAliasWordWrap,
    CSSPropertyID::kAliasGridColumnGap,
    CSSPropertyID::kAliasGridRowGap,
    CSSPropertyID::kAliasGridGap,
};

const CSSPropertyID kCSSComputableProperties[] = {
    CSSPropertyID::kAccentColor,
    CSSPropertyID::kAlignContent,
    CSSPropertyID::kAlignItems,
    CSSPropertyID::kAlignSelf,
    CSSPropertyID::kAlignmentBaseline,
    CSSPropertyID::kAnchorName,
    CSSPropertyID::kAnimationComposition,
    CSSPropertyID::kAnimationDelay,
    CSSPropertyID::kAnimationDirection,
    CSSPropertyID::kAnimationDuration,
    CSSPropertyID::kAnimationFillMode,
    CSSPropertyID::kAnimationIterationCount,
    CSSPropertyID::kAnimationName,
    CSSPropertyID::kAnimationPlayState,
    CSSPropertyID::kAnimationRangeEnd,
    CSSPropertyID::kAnimationRangeStart,
    CSSPropertyID::kAnimationTimeline,
    CSSPropertyID::kAnimationTimingFunction,
    CSSPropertyID::kAppRegion,
    CSSPropertyID::kAppearance,
    CSSPropertyID::kBackdropFilter,
    CSSPropertyID::kBackfaceVisibility,
    CSSPropertyID::kBackgroundAttachment,
    CSSPropertyID::kBackgroundBlendMode,
    CSSPropertyID::kBackgroundClip,
    CSSPropertyID::kBackgroundColor,
    CSSPropertyID::kBackgroundImage,
    CSSPropertyID::kBackgroundOrigin,
    CSSPropertyID::kBackgroundPosition,
    CSSPropertyID::kBackgroundRepeat,
    CSSPropertyID::kBackgroundSize,
    CSSPropertyID::kBaselineShift,
    CSSPropertyID::kBaselineSource,
    CSSPropertyID::kBlockSize,
    CSSPropertyID::kBorderBlockEndColor,
    CSSPropertyID::kBorderBlockEndStyle,
    CSSPropertyID::kBorderBlockEndWidth,
    CSSPropertyID::kBorderBlockStartColor,
    CSSPropertyID::kBorderBlockStartStyle,
    CSSPropertyID::kBorderBlockStartWidth,
    CSSPropertyID::kBorderBottomColor,
    CSSPropertyID::kBorderBottomLeftRadius,
    CSSPropertyID::kBorderBottomRightRadius,
    CSSPropertyID::kBorderBottomStyle,
    CSSPropertyID::kBorderBottomWidth,
    CSSPropertyID::kBorderCollapse,
    CSSPropertyID::kBorderEndEndRadius,
    CSSPropertyID::kBorderEndStartRadius,
    CSSPropertyID::kBorderImageOutset,
    CSSPropertyID::kBorderImageRepeat,
    CSSPropertyID::kBorderImageSlice,
    CSSPropertyID::kBorderImageSource,
    CSSPropertyID::kBorderImageWidth,
    CSSPropertyID::kBorderInlineEndColor,
    CSSPropertyID::kBorderInlineEndStyle,
    CSSPropertyID::kBorderInlineEndWidth,
    CSSPropertyID::kBorderInlineStartColor,
    CSSPropertyID::kBorderInlineStartStyle,
    CSSPropertyID::kBorderInlineStartWidth,
    CSSPropertyID::kBorderLeftColor,
    CSSPropertyID::kBorderLeftStyle,
    CSSPropertyID::kBorderLeftWidth,
    CSSPropertyID::kBorderRightColor,
    CSSPropertyID::kBorderRightStyle,
    CSSPropertyID::kBorderRightWidth,
    CSSPropertyID::kBorderStartEndRadius,
    CSSPropertyID::kBorderStartStartRadius,
    CSSPropertyID::kBorderTopColor,
    CSSPropertyID::kBorderTopLeftRadius,
    CSSPropertyID::kBorderTopRightRadius,
    CSSPropertyID::kBorderTopStyle,
    CSSPropertyID::kBorderTopWidth,
    CSSPropertyID::kBottom,
    CSSPropertyID::kBoxShadow,
    CSSPropertyID::kBoxSizing,
    CSSPropertyID::kBreakAfter,
    CSSPropertyID::kBreakBefore,
    CSSPropertyID::kBreakInside,
    CSSPropertyID::kBufferedRendering,
    CSSPropertyID::kCaptionSide,
    CSSPropertyID::kCaretColor,
    CSSPropertyID::kClear,
    CSSPropertyID::kClip,
    CSSPropertyID::kClipPath,
    CSSPropertyID::kClipRule,
    CSSPropertyID::kColor,
    CSSPropertyID::kColorInterpolation,
    CSSPropertyID::kColorInterpolationFilters,
    CSSPropertyID::kColorRendering,
    CSSPropertyID::kColumnCount,
    CSSPropertyID::kColumnGap,
    CSSPropertyID::kColumnRuleColor,
    CSSPropertyID::kColumnRuleStyle,
    CSSPropertyID::kColumnRuleWidth,
    CSSPropertyID::kColumnSpan,
    CSSPropertyID::kColumnWidth,
    CSSPropertyID::kContainIntrinsicBlockSize,
    CSSPropertyID::kContainIntrinsicHeight,
    CSSPropertyID::kContainIntrinsicInlineSize,
    CSSPropertyID::kContainIntrinsicSize,
    CSSPropertyID::kContainIntrinsicWidth,
    CSSPropertyID::kContainerName,
    CSSPropertyID::kContainerType,
    CSSPropertyID::kContent,
    CSSPropertyID::kCursor,
    CSSPropertyID::kCx,
    CSSPropertyID::kCy,
    CSSPropertyID::kD,
    CSSPropertyID::kDirection,
    CSSPropertyID::kDisplay,
    CSSPropertyID::kDominantBaseline,
    CSSPropertyID::kDynamicRangeLimit,
    CSSPropertyID::kEmptyCells,
    CSSPropertyID::kFieldSizing,
    CSSPropertyID::kFill,
    CSSPropertyID::kFillOpacity,
    CSSPropertyID::kFillRule,
    CSSPropertyID::kFilter,
    CSSPropertyID::kFlexBasis,
    CSSPropertyID::kFlexDirection,
    CSSPropertyID::kFlexGrow,
    CSSPropertyID::kFlexShrink,
    CSSPropertyID::kFlexWrap,
    CSSPropertyID::kFloat,
    CSSPropertyID::kFloodColor,
    CSSPropertyID::kFloodOpacity,
    CSSPropertyID::kFontFamily,
    CSSPropertyID::kFontKerning,
    CSSPropertyID::kFontOpticalSizing,
    CSSPropertyID::kFontPalette,
    CSSPropertyID::kFontSize,
    CSSPropertyID::kFontSizeAdjust,
    CSSPropertyID::kFontStretch,
    CSSPropertyID::kFontStyle,
    CSSPropertyID::kFontSynthesisSmallCaps,
    CSSPropertyID::kFontSynthesisStyle,
    CSSPropertyID::kFontSynthesisWeight,
    CSSPropertyID::kFontVariant,
    CSSPropertyID::kFontVariantAlternates,
    CSSPropertyID::kFontVariantCaps,
    CSSPropertyID::kFontVariantEastAsian,
    CSSPropertyID::kFontVariantEmoji,
    CSSPropertyID::kFontVariantLigatures,
    CSSPropertyID::kFontVariantNumeric,
    CSSPropertyID::kFontVariantPosition,
    CSSPropertyID::kFontWeight,
    CSSPropertyID::kGridAutoColumns,
    CSSPropertyID::kGridAutoFlow,
    CSSPropertyID::kGridAutoRows,
    CSSPropertyID::kGridColumnEnd,
    CSSPropertyID::kGridColumnStart,
    CSSPropertyID::kGridRowEnd,
    CSSPropertyID::kGridRowStart,
    CSSPropertyID::kGridTemplateAreas,
    CSSPropertyID::kGridTemplateColumns,
    CSSPropertyID::kGridTemplateRows,
    CSSPropertyID::kHeight,
    CSSPropertyID::kHyphenateCharacter,
    CSSPropertyID::kHyphenateLimitChars,
    CSSPropertyID::kHyphens,
    CSSPropertyID::kImageOrientation,
    CSSPropertyID::kImageRendering,
    CSSPropertyID::kInitialLetter,
    CSSPropertyID::kInlineSize,
    CSSPropertyID::kInsetArea,
    CSSPropertyID::kInsetBlockEnd,
    CSSPropertyID::kInsetBlockStart,
    CSSPropertyID::kInsetInlineEnd,
    CSSPropertyID::kInsetInlineStart,
    CSSPropertyID::kIsolation,
    CSSPropertyID::kJustifyContent,
    CSSPropertyID::kJustifyItems,
    CSSPropertyID::kJustifySelf,
    CSSPropertyID::kLeft,
    CSSPropertyID::kLetterSpacing,
    CSSPropertyID::kLightingColor,
    CSSPropertyID::kLineBreak,
    CSSPropertyID::kLineClamp,
    CSSPropertyID::kLineHeight,
    CSSPropertyID::kListStyleImage,
    CSSPropertyID::kListStylePosition,
    CSSPropertyID::kListStyleType,
    CSSPropertyID::kMarginBlockEnd,
    CSSPropertyID::kMarginBlockStart,
    CSSPropertyID::kMarginBottom,
    CSSPropertyID::kMarginInlineEnd,
    CSSPropertyID::kMarginInlineStart,
    CSSPropertyID::kMarginLeft,
    CSSPropertyID::kMarginRight,
    CSSPropertyID::kMarginTop,
    CSSPropertyID::kMarkerEnd,
    CSSPropertyID::kMarkerMid,
    CSSPropertyID::kMarkerStart,
    CSSPropertyID::kMaskClip,
    CSSPropertyID::kMaskComposite,
    CSSPropertyID::kMaskImage,
    CSSPropertyID::kMaskMode,
    CSSPropertyID::kMaskOrigin,
    CSSPropertyID::kMaskPosition,
    CSSPropertyID::kMaskRepeat,
    CSSPropertyID::kMaskSize,
    CSSPropertyID::kMaskType,
    CSSPropertyID::kMathDepth,
    CSSPropertyID::kMathShift,
    CSSPropertyID::kMathStyle,
    CSSPropertyID::kMaxBlockSize,
    CSSPropertyID::kMaxHeight,
    CSSPropertyID::kMaxInlineSize,
    CSSPropertyID::kMaxWidth,
    CSSPropertyID::kMinBlockSize,
    CSSPropertyID::kMinHeight,
    CSSPropertyID::kMinInlineSize,
    CSSPropertyID::kMinWidth,
    CSSPropertyID::kMixBlendMode,
    CSSPropertyID::kObjectFit,
    CSSPropertyID::kObjectPosition,
    CSSPropertyID::kObjectViewBox,
    CSSPropertyID::kOffsetAnchor,
    CSSPropertyID::kOffsetDistance,
    CSSPropertyID::kOffsetPath,
    CSSPropertyID::kOffsetPosition,
    CSSPropertyID::kOffsetRotate,
    CSSPropertyID::kOpacity,
    CSSPropertyID::kOrder,
    CSSPropertyID::kOrphans,
    CSSPropertyID::kOutlineColor,
    CSSPropertyID::kOutlineOffset,
    CSSPropertyID::kOutlineStyle,
    CSSPropertyID::kOutlineWidth,
    CSSPropertyID::kOverflowAnchor,
    CSSPropertyID::kOverflowBlock,
    CSSPropertyID::kOverflowClipMargin,
    CSSPropertyID::kOverflowInline,
    CSSPropertyID::kOverflowWrap,
    CSSPropertyID::kOverflowX,
    CSSPropertyID::kOverflowY,
    CSSPropertyID::kOverlay,
    CSSPropertyID::kOverscrollBehaviorBlock,
    CSSPropertyID::kOverscrollBehaviorInline,
    CSSPropertyID::kPaddingBlockEnd,
    CSSPropertyID::kPaddingBlockStart,
    CSSPropertyID::kPaddingBottom,
    CSSPropertyID::kPaddingInlineEnd,
    CSSPropertyID::kPaddingInlineStart,
    CSSPropertyID::kPaddingLeft,
    CSSPropertyID::kPaddingRight,
    CSSPropertyID::kPaddingTop,
    CSSPropertyID::kPaintOrder,
    CSSPropertyID::kPerspective,
    CSSPropertyID::kPerspectiveOrigin,
    CSSPropertyID::kPointerEvents,
    CSSPropertyID::kPopoverHideDelay,
    CSSPropertyID::kPopoverShowDelay,
    CSSPropertyID::kPosition,
    CSSPropertyID::kPositionAnchor,
    CSSPropertyID::kPositionTryOptions,
    CSSPropertyID::kPositionTryOrder,
    CSSPropertyID::kPositionVisibility,
    CSSPropertyID::kR,
    CSSPropertyID::kReadingOrderItems,
    CSSPropertyID::kResize,
    CSSPropertyID::kRight,
    CSSPropertyID::kRotate,
    CSSPropertyID::kRowGap,
    CSSPropertyID::kRubyPosition,
    CSSPropertyID::kRx,
    CSSPropertyID::kRy,
    CSSPropertyID::kScale,
    CSSPropertyID::kScrollBehavior,
    CSSPropertyID::kScrollMarginBlockEnd,
    CSSPropertyID::kScrollMarginBlockStart,
    CSSPropertyID::kScrollMarginInlineEnd,
    CSSPropertyID::kScrollMarginInlineStart,
    CSSPropertyID::kScrollPaddingBlockEnd,
    CSSPropertyID::kScrollPaddingBlockStart,
    CSSPropertyID::kScrollPaddingInlineEnd,
    CSSPropertyID::kScrollPaddingInlineStart,
    CSSPropertyID::kScrollStartBlock,
    CSSPropertyID::kScrollStartInline,
    CSSPropertyID::kScrollStartTargetBlock,
    CSSPropertyID::kScrollStartTargetInline,
    CSSPropertyID::kScrollStartTargetX,
    CSSPropertyID::kScrollStartTargetY,
    CSSPropertyID::kScrollStartX,
    CSSPropertyID::kScrollStartY,
    CSSPropertyID::kScrollTimelineAxis,
    CSSPropertyID::kScrollTimelineName,
    CSSPropertyID::kScrollbarColor,
    CSSPropertyID::kScrollbarGutter,
    CSSPropertyID::kScrollbarWidth,
    CSSPropertyID::kShapeImageThreshold,
    CSSPropertyID::kShapeMargin,
    CSSPropertyID::kShapeOutside,
    CSSPropertyID::kShapeRendering,
    CSSPropertyID::kSpeak,
    CSSPropertyID::kStopColor,
    CSSPropertyID::kStopOpacity,
    CSSPropertyID::kStroke,
    CSSPropertyID::kStrokeDasharray,
    CSSPropertyID::kStrokeDashoffset,
    CSSPropertyID::kStrokeLinecap,
    CSSPropertyID::kStrokeLinejoin,
    CSSPropertyID::kStrokeMiterlimit,
    CSSPropertyID::kStrokeOpacity,
    CSSPropertyID::kStrokeWidth,
    CSSPropertyID::kTabSize,
    CSSPropertyID::kTableLayout,
    CSSPropertyID::kTextAlign,
    CSSPropertyID::kTextAlignLast,
    CSSPropertyID::kTextAnchor,
    CSSPropertyID::kTextAutospace,
    CSSPropertyID::kTextBoxEdge,
    CSSPropertyID::kTextBoxTrim,
    CSSPropertyID::kTextDecoration,
    CSSPropertyID::kTextDecorationColor,
    CSSPropertyID::kTextDecorationLine,
    CSSPropertyID::kTextDecorationSkipInk,
    CSSPropertyID::kTextDecorationStyle,
    CSSPropertyID::kTextEmphasisColor,
    CSSPropertyID::kTextEmphasisPosition,
    CSSPropertyID::kTextEmphasisStyle,
    CSSPropertyID::kTextIndent,
    CSSPropertyID::kTextOverflow,
    CSSPropertyID::kTextRendering,
    CSSPropertyID::kTextShadow,
    CSSPropertyID::kTextSizeAdjust,
    CSSPropertyID::kTextSpacingTrim,
    CSSPropertyID::kTextTransform,
    CSSPropertyID::kTextUnderlinePosition,
    CSSPropertyID::kTextWrap,
    CSSPropertyID::kTimelineScope,
    CSSPropertyID::kTop,
    CSSPropertyID::kTouchAction,
    CSSPropertyID::kTransform,
    CSSPropertyID::kTransformOrigin,
    CSSPropertyID::kTransformStyle,
    CSSPropertyID::kTransitionBehavior,
    CSSPropertyID::kTransitionDelay,
    CSSPropertyID::kTransitionDuration,
    CSSPropertyID::kTransitionProperty,
    CSSPropertyID::kTransitionTimingFunction,
    CSSPropertyID::kTranslate,
    CSSPropertyID::kUnicodeBidi,
    CSSPropertyID::kUserSelect,
    CSSPropertyID::kVectorEffect,
    CSSPropertyID::kVerticalAlign,
    CSSPropertyID::kViewTimelineAxis,
    CSSPropertyID::kViewTimelineInset,
    CSSPropertyID::kViewTimelineName,
    CSSPropertyID::kViewTransitionClass,
    CSSPropertyID::kViewTransitionName,
    CSSPropertyID::kVisibility,
    CSSPropertyID::kWhiteSpaceCollapse,
    CSSPropertyID::kWidows,
    CSSPropertyID::kWidth,
    CSSPropertyID::kWillChange,
    CSSPropertyID::kWordBreak,
    CSSPropertyID::kWordSpacing,
    CSSPropertyID::kWritingMode,
    CSSPropertyID::kX,
    CSSPropertyID::kY,
    CSSPropertyID::kZIndex,
    CSSPropertyID::kZoom,
    CSSPropertyID::kWebkitBorderHorizontalSpacing,
    CSSPropertyID::kWebkitBorderImage,
    CSSPropertyID::kWebkitBorderVerticalSpacing,
    CSSPropertyID::kWebkitBoxAlign,
    CSSPropertyID::kWebkitBoxDecorationBreak,
    CSSPropertyID::kWebkitBoxDirection,
    CSSPropertyID::kWebkitBoxFlex,
    CSSPropertyID::kWebkitBoxOrdinalGroup,
    CSSPropertyID::kWebkitBoxOrient,
    CSSPropertyID::kWebkitBoxPack,
    CSSPropertyID::kWebkitBoxReflect,
    CSSPropertyID::kWebkitFontSmoothing,
    CSSPropertyID::kWebkitLineBreak,
    CSSPropertyID::kWebkitLineClamp,
    CSSPropertyID::kWebkitLocale,
    CSSPropertyID::kWebkitMaskBoxImage,
    CSSPropertyID::kWebkitMaskBoxImageOutset,
    CSSPropertyID::kWebkitMaskBoxImageRepeat,
    CSSPropertyID::kWebkitMaskBoxImageSlice,
    CSSPropertyID::kWebkitMaskBoxImageSource,
    CSSPropertyID::kWebkitMaskBoxImageWidth,
    CSSPropertyID::kWebkitPrintColorAdjust,
    CSSPropertyID::kWebkitRtlOrdering,
    CSSPropertyID::kWebkitTapHighlightColor,
    CSSPropertyID::kWebkitTextCombine,
    CSSPropertyID::kWebkitTextDecorationsInEffect,
    CSSPropertyID::kWebkitTextFillColor,
    CSSPropertyID::kWebkitTextOrientation,
    CSSPropertyID::kWebkitTextSecurity,
    CSSPropertyID::kWebkitTextStrokeColor,
    CSSPropertyID::kWebkitTextStrokeWidth,
    CSSPropertyID::kWebkitUserDrag,
    CSSPropertyID::kWebkitUserModify,
    CSSPropertyID::kWebkitWritingMode,
};

// The lowest CSSPropertyID excluding kInvalid and kVariable.
const int kIntFirstCSSProperty = 2;
const CSSPropertyID kFirstCSSProperty =
    static_cast<CSSPropertyID>(kIntFirstCSSProperty);

// The number of unresolved CSS properties excluding kInvalid and kVariable.
const int kNumCSSProperties = 695;

// The highest resolved CSSPropertyID.
const int kIntLastCSSProperty = 577;
const CSSPropertyID kLastCSSProperty =
    static_cast<CSSPropertyID>(kIntLastCSSProperty);

// The highest unresolved CSSPropertyID.
const CSSPropertyID kLastUnresolvedCSSProperty =
    static_cast<CSSPropertyID>(696);

const CSSPropertyID kFirstHighPriorityCSSProperty = kFirstCSSProperty;
const CSSPropertyID kLastHighPriorityCSSProperty = CSSPropertyID::kZoom;

// 1 + <The highest unresolved CSSPropertyID>.
const int kNumCSSPropertyIDs = static_cast<int>(kLastUnresolvedCSSProperty) + 1;

const size_t kMaxCSSPropertyNameLength = 48;
constexpr size_t kCSSPropertyIDBitLength = 10;
constexpr size_t kMaxShorthandExpansion = 18;

static_assert((static_cast<size_t>(1) << kCSSPropertyIDBitLength) >
                  static_cast<size_t>(kLastUnresolvedCSSProperty),
              "kCSSPropertyIDBitLength has enough bits");

// These are basically just change-detector tests, so that we do not
// accidentally add new high-priority properties or break the code generator.
static_assert(CSSPropertyID::kColorScheme == kFirstHighPriorityCSSProperty);
static_assert(CSSPropertyID::kZoom == kLastHighPriorityCSSProperty);
static_assert((static_cast<int>(kLastHighPriorityCSSProperty) -
               static_cast<int>(kFirstHighPriorityCSSProperty)) == 40,
              "There should a low number of high-priority properties");

inline int GetCSSPropertyIDIndex(CSSPropertyID id) {
    DCHECK_GE(id, kFirstCSSProperty);
    DCHECK_LE(id, kLastCSSProperty);
    return static_cast<int>(id) - kIntFirstCSSProperty;
}

constexpr bool IsHighPriority(CSSPropertyID id) {
  return id >= kFirstHighPriorityCSSProperty &&
      id <= kLastHighPriorityCSSProperty;
}

inline bool IsCSSPropertyIDWithName(CSSPropertyID id)
{
    return id >= kFirstCSSProperty && id <= kLastUnresolvedCSSProperty;
}

inline bool IsValidCSSPropertyID(CSSPropertyID id)
{
    return id != CSSPropertyID::kInvalid;
}

inline CSSPropertyID ConvertToCSSPropertyID(int value)
{
    DCHECK_GE(value, static_cast<int>(CSSPropertyID::kInvalid));
    DCHECK_LE(value, kIntLastCSSProperty);
    return static_cast<CSSPropertyID>(value);
}

int CORE_EXPORT ResolveCSSPropertyAlias(int value);

inline bool IsPropertyAlias(CSSPropertyID id) {
  return static_cast<int>(id) >= 578;
}

inline CSSPropertyID ResolveCSSPropertyID(CSSPropertyID id)
{
  int int_id = static_cast<int>(id);
//  if (IsPropertyAlias(id))
//    int_id = ResolveCSSPropertyAlias(int_id);
  return ConvertToCSSPropertyID(int_id);
}

CSSPropertyID CORE_EXPORT CssPropertyID(const ExecutionContext*,
                                        const WTF::String&);

class CSSPropertyIDList {
  STACK_ALLOCATED();

 public:
  class Iterator {
    STACK_ALLOCATED();
   public:
    Iterator(int id) : id_(id) {}
    CSSPropertyID operator*() const { return ConvertToCSSPropertyID(id_); }
    Iterator& operator++() {
      id_++;
      return *this;
    }
    bool operator!=(const Iterator& i) const { return id_ != i.id_; }

   private:
    int id_;
  };
  Iterator begin() const { return Iterator(kIntFirstCSSProperty); }
  Iterator end() const { return Iterator(kIntLastCSSProperty + 1); }
};

mojom::blink::CSSSampleId CORE_EXPORT GetCSSSampleId(CSSPropertyID id);

}  // namespace blink

#endif  // THIRD_PARTY_BLINK_RENDERER_CORE_CSS_CSS_PROPERTY_NAMES_H_
