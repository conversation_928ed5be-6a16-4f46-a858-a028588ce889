// Copyright 2018 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef THIRD_PARTY_BLINK_RENDERER_CORE_LAYOUT_FLEX_FLEX_LAYOUT_ALGORITHM_H_
#define THIRD_PARTY_BLINK_RENDERER_CORE_LAYOUT_FLEX_FLEX_LAYOUT_ALGORITHM_H_

#include "third_party/blink/renderer/core/core_export.h"
#include "third_party/blink/renderer/core/layout/box_fragment_builder.h"
#include "third_party/blink/renderer/core/layout/flex/flex_break_token_data.h"
#include "third_party/blink/renderer/core/layout/flex/flexible_box_algorithm.h"
#include "third_party/blink/renderer/core/layout/layout_algorithm.h"

namespace blink {

class BlockBreakToken;
class BlockNode;
struct DevtoolsFlexInfo;
struct NGFlexItem;

class CORE_EXPORT FlexLayoutAlgorithm
    : public LayoutAlgorithm<BlockNode, BoxFragmentBuilder, BlockBreakToken> {
 public:
  explicit FlexLayoutAlgorithm(
      const LayoutAlgorithmParams& params,
      const HashMap<wtf_size_t, LayoutUnit>* cross_size_adjustments = nullptr);

  MinMaxSizesResult ComputeMinMaxSizes(const MinMaxSizesFloatInput&);
  const LayoutResult* Layout();

 private:
  const LayoutResult* RelayoutIgnoringChildScrollbarChanges();
  const LayoutResult* RelayoutAndBreakEarlierForFlex(
      const LayoutResult* previous_result);
  const LayoutResult* LayoutInternal();

  void PlaceFlexItems(
      HeapVector<NGFlexLine>* flex_line_outputs,
      HeapVector<Member<LayoutBox>>* oof_children,
      bool is_computing_multiline_column_intrinsic_size = false);

  void CalculateTotalIntrinsicBlockSize(bool use_empty_line_block_size);

  bool DoesItemComputedCrossSizeHaveAuto(const BlockNode& child) const;
  bool DoesItemStretch(const BlockNode& child) const;
  // This checks for one of the scenarios where a flex-item box has a definite
  // size that would be indefinite if the box weren't a flex item.
  // See https://drafts.csswg.org/css-flexbox/#definite-sizes
  bool WillChildCrossSizeBeContainerCrossSize(const BlockNode& child) const;

  bool IsContainerCrossSizeDefinite() const;

  enum class Phase { kLayout, kRowIntrinsicSize, kColumnWrapIntrinsicSize };
  ConstraintSpace BuildSpaceForIntrinsicInlineSize(
      const BlockNode& flex_item) const;
  ConstraintSpace BuildSpaceForFlexBasis(const BlockNode& flex_item) const;
  ConstraintSpace BuildSpaceForIntrinsicBlockSize(
      const BlockNode& flex_item,
      std::optional<LayoutUnit> override_inline_size) const;
  // |line_cross_size_for_stretch| should only be set when running the final
  // layout pass for stretch, when the line cross size is definite.
  // |block_offset_for_fragmentation| should only be set when running the final
  // layout pass for fragmentation. Both may be set at the same time.
  ConstraintSpace BuildSpaceForLayout(
      const BlockNode& flex_item_node,
      LayoutUnit item_main_axis_final_size,
      bool is_initial_block_size_indefinite,
      std::optional<LayoutUnit> override_inline_size = std::nullopt,
      std::optional<LayoutUnit> line_cross_size_for_stretch = std::nullopt,
      std::optional<LayoutUnit> block_offset_for_fragmentation = std::nullopt,
      bool min_block_size_should_encompass_intrinsic_size = false) const;

  void ConstructAndAppendFlexItems(
      Phase phase,
      HeapVector<Member<LayoutBox>>* oof_children = nullptr);
  void ApplyFinalAlignmentAndReversals(
      HeapVector<NGFlexLine>* flex_line_outputs);
  LayoutResult::EStatus GiveItemsFinalPositionAndSize(
      HeapVector<NGFlexLine>* flex_line_outputs,
      Vector<EBreakBetween>* row_break_between_outputs);
  LayoutResult::EStatus GiveItemsFinalPositionAndSizeForFragmentation(
      HeapVector<NGFlexLine>* flex_line_outputs,
      Vector<EBreakBetween>* row_break_between_outputs,
      FlexBreakTokenData::FlexBreakBeforeRow* break_before_row);
  LayoutResult::EStatus PropagateFlexItemInfo(FlexItem* flex_item,
                                              wtf_size_t flex_line_idx,
                                              LogicalOffset offset,
                                              PhysicalSize fragment_size);
//  void LayoutColumnReverse(LayoutUnit main_axis_content_size);
//
  // This is same method as FlexItem but we need that logic before FlexItem is
  // constructed.
  bool MainAxisIsInlineAxis(const BlockNode& child) const;
  LayoutUnit MainAxisContentExtent(LayoutUnit sum_hypothetical_main_size) const;

  void HandleOutOfFlowPositionedItems(
      HeapVector<Member<LayoutBox>>& oof_children);

//  void AdjustButtonBaseline(LayoutUnit final_content_cross_size);
//
//  MinMaxSizesResult ComputeMinMaxSizeOfRowContainerV3();
  MinMaxSizesResult ComputeMinMaxSizeOfMultilineColumnContainer();

  // Return the amount of block space available in the current fragmentainer
  // for the node being laid out by this algorithm.
  LayoutUnit FragmentainerSpaceAvailable(LayoutUnit block_offset) const;

  // Consume all remaining fragmentainer space. This happens when we decide to
  // break before a child.
  //
  // https://www.w3.org/TR/css-break-3/#box-splitting
  void ConsumeRemainingFragmentainerSpace(
      LayoutUnit previously_consumed_block_size,
      NGFlexLine* flex_line,
      const FlexColumnBreakInfo* column_break_info = nullptr);

  // Insert a fragmentainer break before a row if necessary. Rows do not produce
  // a layout result, so when breaking before a row, we will insert a
  // fragmentainer break before the first child in a row. |child| should be
  // those associated with the first child in the row. |row|,
  // |row_block_offset|, |row_break_between|, |row_index|,
  // |has_container_separation| and |is_first_for_row| are specific to the row
  // itself. See
  // |::blink::BreakBeforeChildIfNeeded()| for more documentation.
  BreakStatus BreakBeforeRowIfNeeded(const NGFlexLine& row,
                                     LayoutUnit row_block_offset,
                                     EBreakBetween row_break_between,
                                     wtf_size_t row_index,
                                     LayoutInputNode child,
                                     bool has_container_separation,
                                     bool is_first_for_row);

  // Move past the breakpoint before the row, if possible, and return true. Also
  // update the appeal of breaking before the row (if we're not going
  // to break before it). If false is returned, it means that we need to break
  // before the row (or even earlier). See |::blink::MovePastBreakpoint()| for
  // more documentation.
  bool MovePastRowBreakPoint(BreakAppeal appeal_before,
                             LayoutUnit fragmentainer_block_offset,
                             LayoutUnit row_block_size,
                             wtf_size_t row_index,
                             bool has_container_separation,
                             bool breakable_at_start_of_container);

  // Add an early break for the column at the provided |index|.
  void AddColumnEarlyBreak(EarlyBreak* breakpoint, wtf_size_t index);

  // Add the amount an item expanded by to the item offset adjustment of the
  // flex line at the index directly after |flex_line_idx|, if there is one.
  void AdjustOffsetForNextLine(HeapVector<NGFlexLine>* flex_line_outputs,
                               wtf_size_t flex_line_idx,
                               LayoutUnit item_expansion) const;

  // If a flex item expands past the row cross-size as a result of
  // fragmentation, we will abort and re-run layout with the appropriate row
  // cross-size adjustments.
  const LayoutResult* RelayoutWithNewRowSizes();

  // Used to determine when to allow an item to expand as a result of
  // fragmentation.
  bool MinBlockSizeShouldEncompassIntrinsicSize(const NGFlexItem& item) const;

//#if DCHECK_IS_ON()
//  void CheckFlexLines(HeapVector<NGFlexLine>& flex_line_outputs) const;
//#endif

  // Used when determining the max-content width of a column-wrap flex
  // container.
  LayoutUnit largest_min_content_contribution_;

  const bool is_column_;
  const bool is_horizontal_flow_;
  const bool is_cross_size_definite_;
  const LogicalSize child_percentage_size_;

  bool has_column_percent_flex_basis_ = false;
  bool ignore_child_scrollbar_changes_ = false;

  // This will be set during block fragmentation once we've processed the first
  // flex item in a given line. It is used to check if we're at a valid class A
  // or C breakpoint within a column flex container.
  wtf_size_t last_line_idx_to_process_first_child_ = kNotFound;
  // This will be set during block fragmentation once we've processed the first
  // flex line. It is used to check if we're at a valid class A or C breakpoint
  // within a row flex container.
  bool has_processed_first_line_ = false;

  FlexibleBoxAlgorithm algorithm_;
//  std::unique_ptr<DevtoolsFlexInfo> layout_info_for_devtools_;

  // The block size of the entire flex container (ignoring any fragmentation).
  LayoutUnit total_block_size_;
  // This will be the intrinsic block size in the current fragmentainer, if
  // inside a fragmentation context. Otherwise, it will represent the intrinsic
  // block size for the entire flex container.
  LayoutUnit intrinsic_block_size_;
  // The intrinsic block size for the entire flex container. When not
  // fragmenting, |total_intrinsic_block_size| and |intrinsic_block_size_| will
  // be equivalent.
  LayoutUnit total_intrinsic_block_size_;

  // Only one early break is supported per container. However, we may need to
  // return to an early break within multiple flex columns. This stores the
  // early breaks per column to be used when aborting layout.
  HeapVector<Member<EarlyBreak>> column_early_breaks_;

  // If an item expands past the row block-end, we will re-run layout with the
  // new cross size. Keep track of each such flex line index mapped to how much
  // it should expand by in the next layout pass.
  HashMap<wtf_size_t, LayoutUnit> row_cross_size_updates_;

  // If set, that means that we are re-running layout with updated row
  // cross-sizes. See |row_cross_size_updates_| for what this maps to.
  const HashMap<wtf_size_t, LayoutUnit>* cross_size_adjustments_ = nullptr;
};

}  // namespace blink

#endif  // THIRD_PARTY_BLINK_RENDERER_CORE_LAYOUT_FLEX_FLEX_LAYOUT_ALGORITHM_H_
