// third_party/blink/public/mojom/frame/color_scheme.mojom-blink-forward.h is auto generated by mojom_bindings_generator.py, do not edit

// Copyright 2019 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef THIRD_PARTY_BLINK_PUBLIC_MOJOM_FRAME_COLOR_SCHEME_MOJOM_BLINK_FORWARD_H_
#define THIRD_PARTY_BLINK_PUBLIC_MOJOM_FRAME_COLOR_SCHEME_MOJOM_BLINK_FORWARD_H_

#include <stdint.h>











namespace blink::mojom {

//enum class ColorScheme : int32_t;

//TODO: jz - 以下enum定义源自src/out/gn_arm64/gen/third_party/blink/public/mojom/frame/color_scheme.mojom-shared.h，拷过来使编译通过
enum class ColorScheme : int32_t {
  
  kLight = 0,
  
  kDark = 1,
  kMinValue = 0,
  kMaxValue = 1,
};


}  // blink::mojom


namespace blink::mojom::blink {
using ColorScheme = ColorScheme;



}  // blink::mojom::blink

#endif  // THIRD_PARTY_BLINK_PUBLIC_MOJOM_FRAME_COLOR_SCHEME_MOJOM_BLINK_FORWARD_H_
