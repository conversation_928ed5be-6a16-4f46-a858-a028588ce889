// third_party/blink/public/mojom/use_counter/metrics/css_property_id.mojom-blink-forward.h is auto generated by mojom_bindings_generator.py, do not edit

// Copyright 2019 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef THIRD_PARTY_BLINK_PUBLIC_MOJOM_USE_COUNTER_METRICS_CSS_PROPERTY_ID_MOJOM_BLINK_FORWARD_H_
#define THIRD_PARTY_BLINK_PUBLIC_MOJOM_USE_COUNTER_METRICS_CSS_PROPERTY_ID_MOJOM_BLINK_FORWARD_H_

#include <stdint.h>











namespace blink::mojom {

enum class CSSSampleId : int32_t;


}  // blink::mojom


namespace blink::mojom::blink {
using CSSSampleId = CSSSampleId;



}  // blink::mojom::blink

#endif  // THIRD_PARTY_BLINK_PUBLIC_MOJOM_USE_COUNTER_METRICS_CSS_PROPERTY_ID_MOJOM_BLINK_FORWARD_H_