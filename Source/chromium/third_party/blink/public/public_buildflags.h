// Generated by build/write_buildflag_header.py
// From "//third_party/blink/public:buildflags"

#ifndef THIRD_PARTY_BLINK_PUBLIC_PUBLIC_BUILDFLAGS_H_
#define THIRD_PARTY_BLINK_PUBLIC_PUBLIC_BUILDFLAGS_H_

#include "build/buildflag.h" // IWYU pragma: export

#define BUILDFLAG_INTERNAL_USE_MINIKIN_HYPHENATION() (0)
#define BUILDFLAG_INTERNAL_ENABLE_UNHANDLED_TAP() (0)

#endif  // THIRD_PARTY_BLINK_PUBLIC_PUBLIC_BUILDFLAGS_H_
