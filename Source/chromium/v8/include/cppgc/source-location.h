// Copyright 2020 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef INCLUDE_CPPGC_SOURCE_LOCATION_H_
#define INCLUDE_CPPGC_SOURCE_LOCATION_H_

#include "v8-source-location.h"

namespace cppgc {

using SourceLocation = v8::SourceLocation;

}  // namespace cppgc

#endif  // INCLUDE_CPPGC_SOURCE_LOCATION_H_
