// Generated by partition_alloc/write_buildflag_header.py
// From "//base/allocator/partition_allocator/src/partition_alloc:partition_alloc_buildflags"

#ifndef BASE_ALLOCATOR_PARTITION_ALLOCATOR_SRC_PARTITION_ALLOC_PARTITION_ALLOC_BUILDFLAGS_H_
#define BASE_ALLOCATOR_PARTITION_ALLOCATOR_SRC_PARTITION_ALLOC_PARTITION_ALLOC_BUILDFLAGS_H_

#include "partition_alloc/buildflag.h" // IWYU pragma: export

#include "build/buildflag.h" // IWYU pragma: export

#define PA_BUILDFLAG_INTERNAL_HAS_64_BIT_POINTERS() (1)
#define BUILDFLAG_INTERNAL_HAS_64_BIT_POINTERS() (1)
#define PA_BUILDFLAG_INTERNAL_HAS_MEMORY_TAGGING() (0)
#define BUILD<PERSON>AG_INTERNAL_HAS_MEMORY_TAGGING() (0)
#define PA_BUILDFLAG_INTERNAL_USE_ALLOCATOR_SHIM() (1)
#define BUILDFLAG_INTERNAL_USE_ALLOCATOR_SHIM() (1)
#define PA_BUILDFLAG_INTERNAL_USE_LARGE_EMPTY_SLOT_SPAN_RING() (1)
#define BUILDFLAG_INTERNAL_USE_LARGE_EMPTY_SLOT_SPAN_RING() (1)
#define PA_BUILDFLAG_INTERNAL_USE_PARTITION_ALLOC() (1)
#define BUILDFLAG_INTERNAL_USE_PARTITION_ALLOC() (1)
#define PA_BUILDFLAG_INTERNAL_USE_PARTITION_ALLOC_AS_MALLOC() (0)
#define BUILDFLAG_INTERNAL_USE_PARTITION_ALLOC_AS_MALLOC() (0)
#define PA_BUILDFLAG_INTERNAL_ENABLE_BACKUP_REF_PTR_SUPPORT() (0)
#define BUILDFLAG_INTERNAL_ENABLE_BACKUP_REF_PTR_SUPPORT() (0)
#define PA_BUILDFLAG_INTERNAL_ENABLE_BACKUP_REF_PTR_SLOW_CHECKS() (0)
#define BUILDFLAG_INTERNAL_ENABLE_BACKUP_REF_PTR_SLOW_CHECKS() (0)
#define PA_BUILDFLAG_INTERNAL_ENABLE_BACKUP_REF_PTR_FEATURE_FLAG() (0)
#define BUILDFLAG_INTERNAL_ENABLE_BACKUP_REF_PTR_FEATURE_FLAG() (0)
#define PA_BUILDFLAG_INTERNAL_ENABLE_BACKUP_REF_PTR_INSTANCE_TRACER() (0)
#define BUILDFLAG_INTERNAL_ENABLE_BACKUP_REF_PTR_INSTANCE_TRACER() (0)
#define PA_BUILDFLAG_INTERNAL_ENABLE_DANGLING_RAW_PTR_CHECKS() (0)
#define BUILDFLAG_INTERNAL_ENABLE_DANGLING_RAW_PTR_CHECKS() (0)
#define PA_BUILDFLAG_INTERNAL_ENABLE_DANGLING_RAW_PTR_FEATURE_FLAG() (0)
#define BUILDFLAG_INTERNAL_ENABLE_DANGLING_RAW_PTR_FEATURE_FLAG() (0)
#define PA_BUILDFLAG_INTERNAL_ENABLE_POINTER_SUBTRACTION_CHECK() (0)
#define BUILDFLAG_INTERNAL_ENABLE_POINTER_SUBTRACTION_CHECK() (0)
#define PA_BUILDFLAG_INTERNAL_ENABLE_POINTER_ARITHMETIC_TRAIT_CHECK() (1)
#define BUILDFLAG_INTERNAL_ENABLE_POINTER_ARITHMETIC_TRAIT_CHECK() (1)
#define PA_BUILDFLAG_INTERNAL_BACKUP_REF_PTR_EXTRA_OOB_CHECKS() (0)
#define BUILDFLAG_INTERNAL_BACKUP_REF_PTR_EXTRA_OOB_CHECKS() (0)
#define PA_BUILDFLAG_INTERNAL_BACKUP_REF_PTR_POISON_OOB_PTR() (0)
#define BUILDFLAG_INTERNAL_BACKUP_REF_PTR_POISON_OOB_PTR() (0)
#define PA_BUILDFLAG_INTERNAL_USE_RAW_PTR_BACKUP_REF_IMPL() (0)
#define BUILDFLAG_INTERNAL_USE_RAW_PTR_BACKUP_REF_IMPL() (0)
#define PA_BUILDFLAG_INTERNAL_USE_ASAN_BACKUP_REF_PTR() (0)
#define BUILDFLAG_INTERNAL_USE_ASAN_BACKUP_REF_PTR() (0)
#define PA_BUILDFLAG_INTERNAL_USE_RAW_PTR_ASAN_UNOWNED_IMPL() (0)
#define BUILDFLAG_INTERNAL_USE_RAW_PTR_ASAN_UNOWNED_IMPL() (0)
#define PA_BUILDFLAG_INTERNAL_USE_RAW_PTR_HOOKABLE_IMPL() (0)
#define BUILDFLAG_INTERNAL_USE_RAW_PTR_HOOKABLE_IMPL() (0)
#define PA_BUILDFLAG_INTERNAL_ENABLE_GWP_ASAN_SUPPORT() (0)
#define BUILDFLAG_INTERNAL_ENABLE_GWP_ASAN_SUPPORT() (0)
#define PA_BUILDFLAG_INTERNAL_USE_ASAN_UNOWNED_PTR() (0)
#define BUILDFLAG_INTERNAL_USE_ASAN_UNOWNED_PTR() (0)
#define PA_BUILDFLAG_INTERNAL_FORCE_ENABLE_RAW_PTR_EXCLUSION() (0)
#define BUILDFLAG_INTERNAL_FORCE_ENABLE_RAW_PTR_EXCLUSION() (0)
#define PA_BUILDFLAG_INTERNAL_USE_FULL_MTE() (0)
#define BUILDFLAG_INTERNAL_USE_FULL_MTE() (0)
#define PA_BUILDFLAG_INTERNAL_RECORD_ALLOC_INFO() (0)
#define BUILDFLAG_INTERNAL_RECORD_ALLOC_INFO() (0)
#define PA_BUILDFLAG_INTERNAL_USE_FREESLOT_BITMAP() (0)
#define BUILDFLAG_INTERNAL_USE_FREESLOT_BITMAP() (0)
#define PA_BUILDFLAG_INTERNAL_GLUE_CORE_POOLS() (0)
#define BUILDFLAG_INTERNAL_GLUE_CORE_POOLS() (0)
#define PA_BUILDFLAG_INTERNAL_ENABLE_POINTER_COMPRESSION() (0)
#define BUILDFLAG_INTERNAL_ENABLE_POINTER_COMPRESSION() (0)
#define PA_BUILDFLAG_INTERNAL_ENABLE_SHADOW_METADATA_FOR_64_BITS_POINTERS() (0)
#define BUILDFLAG_INTERNAL_ENABLE_SHADOW_METADATA_FOR_64_BITS_POINTERS() (0)
#define PA_BUILDFLAG_INTERNAL_USE_FREELIST_DISPATCHER() (0)
#define BUILDFLAG_INTERNAL_USE_FREELIST_DISPATCHER() (0)
#define PA_BUILDFLAG_INTERNAL_USE_STARSCAN() (1)
#define BUILDFLAG_INTERNAL_USE_STARSCAN() (1)
#define PA_BUILDFLAG_INTERNAL_STACK_SCAN_SUPPORTED() (1)
#define BUILDFLAG_INTERNAL_STACK_SCAN_SUPPORTED() (1)
#define PA_BUILDFLAG_INTERNAL_ENABLE_PKEYS() (0)
#define BUILDFLAG_INTERNAL_ENABLE_PKEYS() (0)
#define PA_BUILDFLAG_INTERNAL_ENABLE_THREAD_ISOLATION() (0)
#define BUILDFLAG_INTERNAL_ENABLE_THREAD_ISOLATION() (0)
#define PA_BUILDFLAG_INTERNAL_FORWARD_THROUGH_MALLOC() (0)
#define BUILDFLAG_INTERNAL_FORWARD_THROUGH_MALLOC() (0)
#define PA_BUILDFLAG_INTERNAL_ASSERT_CPP_20() (1)
#define BUILDFLAG_INTERNAL_ASSERT_CPP_20() (1)

#endif  // BASE_ALLOCATOR_PARTITION_ALLOCATOR_SRC_PARTITION_ALLOC_PARTITION_ALLOC_BUILDFLAGS_H_
