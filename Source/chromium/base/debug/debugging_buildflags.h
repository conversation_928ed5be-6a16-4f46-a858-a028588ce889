// Generated by build/write_buildflag_header.py
// From "//base:debugging_buildflags"

#ifndef BASE_DEBUG_DEBUGGING_BUILDFLAGS_H_
#define BASE_DEBUG_DEBUGGING_BUILDFLAGS_H_

#include "build/buildflag.h" // IWYU pragma: export

#define BUILDFLAG_INTERNAL_DCHECK_IS_CONFIGURABLE() (0)
#define BUILDFLAG_INTERNAL_ENABLE_PROFILING() (0)
#define BUILDFLAG_INTERNAL_CAN_UNWIND_WITH_FRAME_POINTERS() (1)
#define BUILDFLAG_INTERNAL_UNSAFE_DEVELOPER_BUILD() (1)
#define BUILDFLAG_INTERNAL_CAN_UNWIND_WITH_CFI_TABLE() (0)
#define BUILDFLAG_INTERNAL_EXCLUDE_UNWIND_TABLES() (0)
#define BUILDFLAG_INTERNAL_ENABLE_GDBINIT_WARNING() (1)
#define BUILDFLAG_INTERNAL_ENABLE_LLDBINIT_WARNING() (1)
#define BUILDFLAG_INTERNAL_EXPENSIVE_DCHECKS_ARE_ON() (0)
#define BUILDFLAG_INTERNAL_ENABLE_STACK_TRACE_LINE_NUMBERS() (1)
#define BUILDFLAG_INTERNAL_ENABLE_COMMANDLINE_SEQUENCE_CHECKS() (1)
#define BUILDFLAG_INTERNAL_ENABLE_ALLOCATION_STACK_TRACE_RECORDER() (0)
#define BUILDFLAG_INTERNAL_ENABLE_ALLOCATION_TRACE_RECORDER_FULL_REPORTING() (0)

#endif  // BASE_DEBUG_DEBUGGING_BUILDFLAGS_H_
