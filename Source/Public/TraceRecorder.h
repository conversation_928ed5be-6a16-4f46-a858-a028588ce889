#ifndef TRACE_RECORDER_H
#define TRACE_RECORDER_H

#include <string>
#include <unordered_map>
#include <chrono>
#include <mutex>
#include <vector>
#include <atomic>
#include <deque>
#include <cstdint>

namespace trace {

// 埋点等级枚举
enum class TraceLevel {
    P0 = 0,  // 最高优先级
    P1 = 1,  // 中等优先级
    P2 = 2   // 低优先级
};

// 埋点等级位掩码常量
const uint8_t TRACE_LEVEL_P0 = 1 << 0;  // 0x01
const uint8_t TRACE_LEVEL_P1 = 1 << 1;  // 0x02
const uint8_t TRACE_LEVEL_P2 = 1 << 2;  // 0x04
const uint8_t TRACE_LEVEL_ALL = TRACE_LEVEL_P0 | TRACE_LEVEL_P1 | TRACE_LEVEL_P2;  // 0x07
const uint8_t TRACE_LEVEL_NONE = 0;     // 0x00

// 单个埋点记录数据
struct TraceData {
    std::string key;
    TraceLevel level;
    uint64_t total_time_ns;  // 总耗时（纳秒）
    uint32_t count;          // 执行次数
    int32_t batch;           // 批次字段
    
    TraceData() : level(TraceLevel::P0), total_time_ns(0), count(0), batch(0) {}
    TraceData(const std::string& k, TraceLevel l) 
        : key(k), level(l), total_time_ns(0), count(0), batch(0) {}
    TraceData(const std::string& k, TraceLevel l, int32_t b)
        : key(k), level(l), total_time_ns(0), count(0), batch(b) {}
};

// 用于生成唯一的key-batch组合键
struct TraceBatchKey {
    std::string key;
    int32_t batch;

    TraceBatchKey(const std::string& k, int32_t b) : key(k), batch(b) {}

    bool operator==(const TraceBatchKey& other) const {
        return key == other.key && batch == other.batch;
    }
};

// 为TraceBatchKey提供hash函数
struct TraceBatchKeyHash {
    std::size_t operator()(const TraceBatchKey& tbk) const {
        return std::hash<std::string>()(tbk.key) ^ (std::hash<int32_t>()(tbk.batch) << 1);
    }
};

// 埋点记录器主类
class TraceRecorder {
public:
    // 构造函数和析构函数
    TraceRecorder();
    ~TraceRecorder() = default;
    
    // 设置启用的埋点等级（使用位掩码）
    void setEnabledLevels(uint8_t enabled_mask);

    // 关闭所有埋点
    void disableAll();
    
    // 开始记录某个key的埋点
    void beginTrace(const std::string& key, TraceLevel level);

    // 结束记录某个key的埋点
    void endTrace(const std::string& key);
    
    // 获取所有埋点数据的二进制流
    std::vector<uint8_t> exportBinaryData();
    
    // 清空所有埋点数据
    void clear();
    
    // 获取指定key的数据（返回所有批次的数据）
    std::vector<TraceData> getTraceData(const std::string& key);
    
    // 获取指定key和批次的数据
    TraceData getTraceData(const std::string& key, int32_t batch);

    // 获取所有数据
    std::vector<TraceData> getAllTraceData();

    // 检查指定等级是否启用
    bool isLevelEnabled(TraceLevel level);

    // 检查是否有任何trace等级被启用
    bool isTraceEnabled();

    // 获取当前启用的等级掩码
    uint8_t getEnabledLevels();

    // 批次管理方法
    void addBatch(int32_t batch);           // 添加批次到队列头部
    void clearTailBatch();                  // 清除队列尾部的批次
    int32_t getCurrentBatch();              // 获取当前使用的批次（队列尾部）

private:
    std::mutex mutex_;
    // 使用key-batch组合作为唯一标识
    std::unordered_map<TraceBatchKey, TraceData, TraceBatchKeyHash> trace_data_;
    // 开始时间记录也需要使用key-batch组合
    std::unordered_map<TraceBatchKey, std::chrono::high_resolution_clock::time_point, TraceBatchKeyHash> start_times_;
    
    // 使用原子位掩码替代多个 bool
    std::atomic<uint8_t> enabled_levels_;

    // 批次队列管理 - 使用deque
    std::deque<int32_t> batch_deque_;       // 批次双端队列
    std::mutex batch_mutex_;                // 批次队列的互斥锁
};

} // namespace trace

#endif // TRACE_RECORDER_H
