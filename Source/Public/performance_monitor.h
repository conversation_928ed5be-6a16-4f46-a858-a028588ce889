//
//  performance_monitor.h
//  MSCRenderer
//
//  Created by <PERSON><PERSON> on 2024/12/30.
//

#ifndef performance_monitor_h
#define performance_monitor_h

#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <mutex>
#include <atomic>
#include <chrono>
#include "system_trace.h"
#ifdef __APPLE__
#include <os/log.h>
#else
#include "MSCRenderer.h"
#include <string>
#endif

#if defined(__APPLE__)
#define ENABLE_PERF_MONITOR
#endif

using namespace std;

namespace blink {

namespace mt {

#if defined(ENABLE_PERF_MONITOR) && defined(__APPLE__)

class PerformanceMonitor
{
public:
    enum EventType {
        Start,
        End
    };

    struct Event {
        Event(EventType event_type, int token, const char* event_name, int event_group_id) :
            event_type_(event_type),
            token_(token),
            time_(std::chrono::high_resolution_clock::now()),
            event_name_(event_name),
            event_group_id_(event_group_id) {
        }

        Event(const Event &other) :
            event_type_(other.event_type_),
            token_(other.token_),
            time_(other.time_),
            event_name_(other.event_name_),
            event_group_id_(other.event_group_id_) {}

        EventType event_type_;
        int token_;
        chrono::steady_clock::time_point time_;
        string event_name_;
        int event_group_id_;
    };

    static int create() {
        std::lock_guard<std::mutex> lock(mtx_);
        auto perf_id = ++perf_id_base_;
        monitors_.insert(make_pair(perf_id, make_shared<PerformanceMonitor>()));
        return perf_id;
    }

    static void destroy(int perf_id) {
        std::lock_guard<std::mutex> lock(mtx_);
        auto iter = monitors_.find(perf_id);
        if (iter == monitors_.end()) {
            return;
        }
        monitors_.erase(iter);
    }

    static int createToken() {
        return counter_.fetch_add(1);
    }

    static void beginEvent(int perf_id, int token, const char* event_name, int event_group_id) {
        addEvent(perf_id, EventType::Start, token, event_name, event_group_id);
    }

    static void endEvent(int perf_id, int token, const char* event_name, int event_group_id) {
        addEvent(perf_id, EventType::End, token, event_name, event_group_id);
    }

    static void addEvent(int perf_id, EventType event_type, int token, const char* event_name,
                         int event_group_id) {
        std::lock_guard<std::mutex> lock(mtx_);
        auto iter = monitors_.find(perf_id);
        if (iter == monitors_.end()) {
            return;
        }
        iter->second->events_.push_back(Event(event_type, token, event_name, event_group_id));
    }

    static void printAndClearEvents(int perf_id);

private:
    static os_log_t logger_;
    static std::mutex mtx_;
    static int perf_id_base_;
    static unordered_map<int, shared_ptr<PerformanceMonitor>> monitors_;
    static std::atomic<int> counter_;

    vector<Event> events_;
};
#elif defined(ENABLE_PERF_MONITOR)
class PerformanceMonitor
{
public:
    enum EventType {
        Start,
        End
    };

    static long create() {
        return 0;
    }

    static void destroy(long perf_id) {}

    static long createToken() {
        return 0;
    }

    static void beginEvent(long perf_id, long token, const char *event_name, long event_group_id) {
        if (strcmp(event_name, "dom") == 0) {
            return;
        }
        atrace::BeginTrace(event_name);
//        beginEventNative(token, event_name, event_group_id);
    }

    static void endEvent(long perf_id, long token, const char *event_name, long event_group_id) {
        if (strcmp(event_name, "dom") == 0) {
            return;
        }
//        endEventNative(token, event_name, event_group_id);
        atrace::EndTrace();
    }

    static void addEvent(long perf_id, EventType event_type, long token, const char* event_name,
                         long event_group_id) {}

    static void printAndClearEvents(long perf_id);
};

#else

class PerformanceMonitor
{
public:
    enum EventType {
        Start,
        End
    };

    static int create() {
        return 0;
    }

    static void destroy(int perf_id) {}

    static int createToken() {
        return 0;
    }

    static void beginEvent(int perf_id, int token, const char* event_name, int event_group_id) {}

    static void endEvent(int perf_id, int token, const char* event_name, int event_group_id) {}

    static void addEvent(int perf_id, EventType event_type, int token, const char* event_name,
                         int event_group_id) {}

    static void printAndClearEvents(int perf_id) {}
};

#endif

}; // namespace mt

}; // namespace blink

#endif
