//
//  props.h
//  MSCRenderer
//
//  Created by <PERSON><PERSON> on 2025/3/3.
//

#ifndef props_h
#define props_h

#ifdef __OBJC__
#include <Foundation/Foundation.h>
#endif

#include <memory>
#include <unordered_map>
#include <sstream>

namespace blink::mt {

using namespace std;

struct PropValue;

namespace PropValueType {

typedef std::monostate Null;
typedef bool Boolean;
typedef double Number;
typedef std::string String;
typedef std::vector<PropValue> Array;
typedef std::unordered_map<std::string, PropValue> Dictionary;

};

using PropValueBase = std::variant<
  PropValueType::Null,
  PropValueType::Boolean,
  PropValueType::Number,
  PropValueType::String,
  PropValueType::Array,
  PropValueType::Dictionary
>;

struct PropValue : PropValueBase {
  using PropValueBase::PropValueBase;

public:
  static PropValue& null() {
    static PropValue empty;
    return empty;
  }

  bool isNull() const {
    return *this == PropValue::null();
  }

  template <typename Type>
  bool isA() const {
    return std::visit([](auto&& arg) -> bool {
      using T = std::decay_t<decltype(arg)>;
      if constexpr (std::is_same_v<T, Type>) {
        return true;
      } else {
        return false;
      }
    }, *this);
  }

  bool boolValue() const {
    return std::visit([this](auto&& arg) -> bool {
      using T = std::decay_t<decltype(arg)>;
      if constexpr (std::is_same_v<T, bool>) {
        return arg;
      } else if constexpr (std::is_same_v<T, double>) {
        return fabs(arg) < std::numeric_limits<double>::epsilon();
      } else if constexpr (std::is_same_v<T, std::string>) {
        const std::string& str = arg;
        return str.length() > 0;
      } else {
        return !isNull();
      }
    }, *this);
  }

  const PropValueType::String& stringValue() const {
    return std::visit([](auto&& arg) -> const PropValueType::String& {
      using T = std::decay_t<decltype(arg)>;
      if constexpr (std::is_same_v<T, PropValueType::String>) {
        return arg;
      } else {
        static const PropValueType::String empty = "";
        return empty;
      }
    }, *this);
  }

  const PropValueType::Number numberValue() const {
    return std::visit([](auto&& arg) -> const PropValueType::Number {
      using T = std::decay_t<decltype(arg)>;
      if constexpr (std::is_same_v<T, PropValueType::Number>) {
        return arg;
      } else {
        return 0;
      }
    }, *this);
  }

  const PropValueType::Array& arrayValue() const {
    return std::visit([](auto&& arg) -> const PropValueType::Array& {
      using T = std::decay_t<decltype(arg)>;
      if constexpr (std::is_same_v<T, std::vector<PropValue>>) {
        return arg;
      } else {
        static const PropValueType::Array empty;
        return empty;
      }
    }, *this);
  }

  const PropValueType::Dictionary& dictionaryValue() const {
    return std::visit([](auto&& arg) -> const PropValueType::Dictionary& {
      using T = std::decay_t<decltype(arg)>;
      if constexpr (std::is_same_v<T, std::unordered_map<std::string, PropValue>>) {
        return arg;
      } else {
        static const PropValueType::Dictionary empty;
        return empty;
      }
    }, *this);
  }
};

class PropsBuilder;

class Props {
public:
  Props() = default;

  long size() const {
    return props_.size();
  }

  const mt::PropValue& getProp(const std::string &name) const {
    auto it = props_.find(name);
    if (it == props_.end()) {
      return mt::PropValue::null();
    }
    return it->second;
  }

  void forEach(std::function<void(const std::string&, const PropValue&)> callback) const {
    for (const auto& pair : props_) {
      callback(pair.first, pair.second);
    }
  }

  std::string GetDescription() const {
    std::stringstream ss;
    ss << "{";
    for (const auto& pair : props_) {
      ss << pair.first << ": " << pair.second.stringValue() << ", ";
    }
    ss << "}";
    return ss.str();
  }
  
  const PropValueType::Dictionary& asDict() const {
    return props_;
  }

private:
  template <typename Type>
  void setProp(const std::string &name, const Type &value) {
      auto iter = props_.find(name);
      if (iter != props_.end()) {
        iter->second = value;
      } else {
        props_.insert(std::make_pair(name, value));
      }
  }
  
  void removeProp(const std::string &name) {
    props_.erase(name);
  }

  std::unordered_map<std::string, PropValue> props_;

  friend class PropsBuilder;
};

class PropsBuilder {
public:
  PropsBuilder(): props_(std::make_shared<Props>()) {}
  
  PropsBuilder(const std::shared_ptr<const Props>& a, const std::shared_ptr<const Props>& b):
    props_(std::make_shared<Props>()) {
    a->forEach([this](const std::string& key, const PropValue& value) {
      this->props_->setProp(key, value);
    });
    b->forEach([this](const std::string& key, const PropValue& value) {
      this->props_->setProp(key, value);
    });
  }

  bool hasProp(const std::string& key) {
    return !props_->getProp(key).isNull();
  }

  void setProp(std::string key, bool value) {
    setProp(key, (PropValueType::Number)value);
  }

  void setProp(std::string key, int value) {
    setProp(key, (PropValueType::Number)value);
  }

  void setProp(std::string key, float value) {
    setProp(key, (PropValueType::Number)value);
  }

  template <typename Type>
  void setProp(const std::string &name, const Type &value) {
    props_->setProp(name, value);
  }

  void removeProp(const std::string &name) {
    props_->removeProp(name);
  }
  
  const std::shared_ptr<const Props> getProps() const {
    return props_;
  }

private:
  std::shared_ptr<Props> props_;
};

std::string propsToJson(const shared_ptr<const blink::mt::Props>& props);

const std::shared_ptr<const blink::mt::Props> jsonToProps(const std::string& json_string);

#ifdef __OBJC__
const shared_ptr<const blink::mt::Props> propsFromDict(NSDictionary *dict);
NSDictionary* propsToDict(shared_ptr<const blink::mt::Props> props);
#endif

}; // namespace blink::mt

#endif /* props_h */
