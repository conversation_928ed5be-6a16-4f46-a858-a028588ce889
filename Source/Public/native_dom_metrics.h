//
//  msc_native_dom_metrics.h
//  MSCRenderer
//
//  Created by qinqiwei02 on 2025/6/10.
//

#include <functional>

#ifndef NATIVE_DOM_METRICS_H
#define NATIVE_DOM_METRICS_H

namespace msc {

// key or type: "msc.render.native.uicmd.error.count"
class NativeDOMMetrics {
 public:
  // errcode. see iOS MSCRenderProfileCode
  enum class ErrCode : int {
    None = 0,
    // JS 注入
    JSInject = -6000,  // NativeDOM JS 注入异常
    // API 处理
    InvalidDocumentId = -6001,
    InvalidParams = -6002,
    // 转到blink侧处理
    MTDocumentNotFound = -6100,
    MTDocumentNodeNotFound = -6103,
    MTDocumentNoLayoutBox = -6104,
    MTDocumentObserverNotFound = -6105
  };

  // native_stack.
  class API {
   public:
    static const std::string kNone;
    // JS 注入
    static const std::string kSetupJSContext;
    static const std::string kRegisterDOMDocument;
    // API 处理
    static const std::string kDocumentQueryEnhanced;
    static const std::string kGetDocument;
    // 转到blink侧处理
    static const std::string kMTDocumentQueryEnhanced;
    static const std::string kMTDocumentIntersectionObserverObserve;
  };

  // message.
  class Message {
   public:
    static const std::string kNone;
    // JS 注入
    static const std::string kCreateJSObject;
    // API 处理
    static const std::string kInvalidPageId;
  };

  struct Tags {
    ErrCode errcode{ErrCode::None};  // NativeDOM error, with the error type
    std::string native_stack;  // API, component or property, or their name
    std::string message;
  };

  using Handle = std::function<void(Tags &&)>;

 private:
  Handle handle_;

 public:
  NativeDOMMetrics() = default;
  void SetHandle(Handle &&handle) { this->handle_ = std::move(handle); }
  bool IsHandleSet() { return this->handle_ != nullptr; }
  void Destory() { handle_ = nullptr; }
  void ReportError(Tags &&tags) {
    if (this->IsHandleSet()) this->handle_(std::move(tags));
  }
};  // class NativeMetrics

}  // namespace msc

#endif
