//
//  types_def.h
//  MSCRenderer
//
//  Created by <PERSON><PERSON> on 2025/3/26.
//

#ifndef types_def_h
#define types_def_h

#include "types.h"
#include "props.h"

#include <functional>
#include <vector>

namespace msc::native_dom {
class Element;
};
namespace blink::mt {

using PageId = int;
using Tag = int;

using QuerySelectorCallback =
    std::function<void(const int& tag, const std::pair<float, float>& location,
                       const std::pair<float, float>& size)>;

using QuerySelectorsCallback =
    std::function<void(const std::vector<int>& tags,
                       const std::vector<std::pair<float, float>>& locations,
                       const std::vector<std::pair<float, float>>& sizes)>;

struct Size {
public:
  float width;
  float height;
  float density;

  Size(): width(0), height(0), density(1){}

  Size(float w, float h): width(w), height(h), density(1) {}

  Size(float w, float h, float density): width(w), height(h), density(density) {}

  bool operator==(const Size& other) const {
    return (width == other.width && height == other.height && density == other.density);
  }
};

struct QueryEnhancedParams {
public:
  std::vector<int> tags;
  bool need_location;
  bool need_size;
  bool need_scroll_offset;
  bool is_viewport;
};

struct QueryEnhancedEntry {
  float x;
  float y;
  float width;
  float height;
  float scroll_top;
  float scroll_left;
  bool invalid{false};
};

using QueryEnhancedEntries = std::vector<QueryEnhancedEntry>;

using QueryEnhancedCallback =
    std::function<void(const QueryEnhancedEntries& entries, msc::native_dom::Element* element)>;

struct IntersectionEntryData {
  struct Rect {
    double x;
    double y;
    double width;
    double height;
  };
  double intersection_ratio;
  Rect bounding_client_rect;
  Rect root_bounds_rect;
  Rect intersection_rect;
  bool is_intersecting;
  bool is_visible;
  int target_tag;
};

using IntersectionObserverCallback = std::function<void(
    const std::vector<blink::mt::IntersectionEntryData>& entries)>;

struct CreateIntersectionObserverParams {
 public:
  Tag root_tag_{};
  bool is_viewport_{};
  std::string margins_str_{};  // root margins
  std::vector<float> margins_{};  // root margins
  std::vector<float> thresholds_{};
  int intersection_observer_id_{0};
};

// create
struct AnimationProperties {
 public:
  std::shared_ptr<Props> props;
};

struct ClearAnimationProperties {
public:
  std::shared_ptr<Props> props;
};

struct Location {
  float x;
  float y;

  Location(): x(0), y(0) {}

  Location(float _x, float _y): x(_x), y(_y) {}

  bool operator==(const Location& other) const {
    return x == other.x && y == other.y;
  }
};

struct Rect {
  Location location;
  Size size;

  Rect(): location(0, 0), size(0, 0) {}

  Rect(float _x, float _y, float _w, float _h): location(_x, _y), size(_w, _h) {}

  bool operator==(const Rect& other) const {
    return location == other.location && size == other.size;
  }
};

struct NativeStyle {
public:
  // 基本
  bool hidden;
  float opacity;

#if defined(__OBJC__)
  std::string overflow_x;
  std::string overflow_y;

  // 文本
  std::string font_style;
  int font_size;
  NSTextAlignment text_align;
  UIColor *color;

  // 背景
  UIColor *background_color;
  std::string background_image;
  NSDictionary *background_gradient;
  std::string background_repeat_x;
  std::string background_repeat_y;
  NSDictionary *background_size;

  // 边框
  MSCBorderStyle border_top_style;
  MSCBorderStyle border_bottom_style;
  MSCBorderStyle border_left_style;
  MSCBorderStyle border_right_style;
  double border_top_width;
  double border_bottom_width;
  double border_left_width;
  double border_right_width;
  UIColor *border_top_color;
  UIColor *border_bottom_color;
  UIColor *border_left_color;
  UIColor *border_right_color;
  float border_top_left_radius;
  float border_top_right_radius;
  float border_bottom_left_radius;
  float border_bottom_right_radius;

  // 效果
  NSDictionary *box_shadow;
  CATransform3D transform;

  // 事件
  MSCPointerEvents pointer_events;
#else
  EOverflow overflow_x;
  EOverflow overflow_y;

  // 绘制
  Color color;
  Color default_color;
  Color background_color;
  std::string background_image;
  scoped_refptr<Gradient> background_gradient;
  FillSize background_size;
  EFillRepeat background_repeat_x;
  EFillRepeat background_repeat_y;

  //Border相关
  EBorderStyle border_top_style;
  EBorderStyle border_bottom_style;
  EBorderStyle border_left_style;
  EBorderStyle border_right_style;

  double border_top_width;
  double border_bottom_width;
  double border_left_width;
  double border_right_width;

  Color border_top_color;
  Color border_bottom_color;
  Color border_left_color;
  Color border_right_color;
  Color default_border_color;

  LengthSize border_top_left_radius;
  LengthSize border_top_right_radius;
  LengthSize border_bottom_left_radius;
  LengthSize border_bottom_right_radius;

  //// 文字排版
  std::string font_style;
  int font_size;
  ETextAlign text_align;
  ShadowList *box_shadow;
  std::vector<Color> shadow_color_list;
  TransformOperations transform;

  EPointerEvents pointer_events;

  bool is_text;
  float text_padding_top;
  float text_padding_bottom;
  float text_padding_left;
  float text_padding_right;
  bool is_msi;

#endif
};

struct NativeTransform {
public:
  enum Type {
    Translate,
    Scale,
    Rotate,
    Skew,
    Matrix,
    Matrix3D,
    Perspective
  };

  NativeTransform(Type _type) : type(_type) {}

  Type type;
};

using NativeTransforms = std::shared_ptr<std::vector<std::shared_ptr<NativeTransform>>>;

struct TranslateTransform : public NativeTransform {
public:
  TranslateTransform() : NativeTransform(TranslateTransform::Type::Translate), x(0), y(0), z(0) {}

  float x;
  float y;
  float z;
};

struct ScaleTransform : public NativeTransform {
public:
  ScaleTransform() : NativeTransform(TranslateTransform::Type::Scale), x(1), y(1), z(1) {}

  float x;
  float y;
  float z;
};

struct RotateTransform : public NativeTransform {
public:
  RotateTransform() : NativeTransform(TranslateTransform::Type::Rotate), angle(0), x(1), y(1), z(1) {}

  float angle;
  float x;
  float y;
  float z;
};

struct SkewTransform : public NativeTransform {
public:
  SkewTransform() : NativeTransform(TranslateTransform::Type::Skew), angle_x(0), angle_y(0) {}

  float angle_x;
  float angle_y;
};

struct PerspectiveTransform : public NativeTransform {
public:
  PerspectiveTransform() : NativeTransform(TranslateTransform::Type::Perspective), value(0) {}

  float value;
};

struct MatrixTansform : public NativeTransform {
public:
  MatrixTansform()
  : NativeTransform(TranslateTransform::Type::Matrix)
  , value00(0)
  , value01(0)
  , value10(0)
  , value11(0)
  , value30(0)
  , value31(0) {}

  float value00;
  float value01;
  float value10;
  float value11;
  float value30;
  float value31;
};

struct Matrix3DTansform : public NativeTransform {
public:
  Matrix3DTansform()
  : NativeTransform(TranslateTransform::Type::Matrix3D)
  , value00(0)
  , value01(0)
  , value02(0)
  , value03(0)
  , value10(0)
  , value11(0)
  , value12(0)
  , value13(0)
  , value20(0)
  , value21(0)
  , value22(0)
  , value23(0)
  , value30(0)
  , value31(0)
  , value32(0)
  , value33(0) {}

  float value00;
  float value01;
  float value02;
  float value03;
  float value10;
  float value11;
  float value12;
  float value13;
  float value20;
  float value21;
  float value22;
  float value23;
  float value30;
  float value31;
  float value32;
  float value33;
};

enum class LayoutReason {
  BatchDidComplete,
  PreBatchDidComplete,
  WXSSetStyle
};

using UICommandID = unsigned int;

enum class UICommandType {
  CreateView,
  UpdateView,
  DeleteView,
  InsertChildViews,
  RemoveChildViews,
  UpdateViewStyle,
  UpdateViewTransform,
  UpdateViewFrame,
  UpdateText,
  BatchDidFinish,
  CreateKeyframesAnimation,
  ClearKeyframesAnimation,
  FlushUITasks
};

struct UICommand {
public:
  UICommand(UICommandID _id, UICommandType _type, Tag _tag) : id(_id), type(_type), tag(_tag) {}

  UICommandID id;
  UICommandType type;
  Tag tag;
};

using UICommandCollection = std::vector<std::shared_ptr<UICommand>>;
using UICommands = std::shared_ptr<UICommandCollection>;
using UICommandBufferCallback = std::function<void(const UICommands& buffer)>;

struct CreateViewCommamnd : public UICommand {
public:
  CreateViewCommamnd(UICommandID _id,
                     UICommandType _type,
                     Tag _tag,
                     const std::string& _view_name,
                     const std::shared_ptr<const Props>& _props)
  : UICommand(_id, _type, _tag), view_name(_view_name), props(_props) {}

  std::string view_name;
  std::shared_ptr<const Props> props;
};

struct UpdateViewCommamnd : public UICommand {
public:
  UpdateViewCommamnd(UICommandID _id,
                     UICommandType _type,
                     Tag _tag,
                     const std::string& _view_name,
                     const std::shared_ptr<const Props>& _props)
  : UICommand(_id, _type, _tag), view_name(_view_name), props(_props) {}

  std::string view_name;
  std::shared_ptr<const Props> props;
};

struct DeleteViewsCommamnd : public UICommand {
  using DeleteOp = Tag;

  DeleteViewsCommamnd(UICommandID _id, UICommandType _type, std::vector<DeleteOp>&& _delete_ops)
  : UICommand(_id, _type, 0), delete_ops(std::move(_delete_ops)) {}

  std::vector<DeleteOp> delete_ops;
};

struct InsertChildViewsCommand : public UICommand {
  struct InsertOp {
    Tag tag;
    int index;
    InsertOp(int t, int i) : tag(t), index(i) {}
  };

  InsertChildViewsCommand(UICommandID _id,
                          UICommandType _type,
                          Tag _tag,
                          std::vector<InsertOp>&& _insert_ops)
  : UICommand(_id, _type, _tag), insert_ops(std::move(_insert_ops)) {}

  std::vector<InsertOp> insert_ops;
};

struct RemoveChildViewsCommand : public UICommand {
  using RemoveOp = int;

  RemoveChildViewsCommand(UICommandID _id,
                          UICommandType _type,
                          Tag _tag,
                          std::vector<RemoveOp>&& _remove_ops)
  : UICommand(_id, _type, _tag), remove_ops(std::move(_remove_ops)) {}

  std::vector<RemoveOp> remove_ops;
};

struct UpdateViewStyleCommamnd : public UICommand {
public:
  UpdateViewStyleCommamnd(UICommandID _id, UICommandType _type, Tag _tag, const shared_ptr<const NativeStyle>& _style)
  : UICommand(_id, _type, _tag), style(_style) {}

  shared_ptr<const NativeStyle> style;
};

struct UpdateViewTransformCommamnd : public UICommand {
public:
  UpdateViewTransformCommamnd(UICommandID _id, UICommandType _type, Tag _tag, const NativeTransforms& _transforms)
  : UICommand(_id, _type, _tag), transforms(_transforms) {}

  NativeTransforms transforms;
};

struct UpdateViewFrameCommamnd : public UICommand {
public:
  UpdateViewFrameCommamnd(UICommandID _id, UICommandType _type, Tag _tag,
                          float _x, float _y, float _width, float _height, bool _hidden)
  : UICommand(_id, _type, _tag), x(_x), y(_y), width(_width), height(_height), hidden(_hidden) {}

  float x;
  float y;
  float width;
  float height;
  bool hidden;
};

struct UpdateTextCommamnd : public UICommand {
public:
  UpdateTextCommamnd(UICommandID _id, UICommandType _type, Tag _tag,
                     float _x, float _y, float _width, float _height,
                     float _top_inset, float _left_inset, float _bottom_inset, float _right_inset,
                     const void* _string_data)
  : UICommand(_id, _type, _tag),
    x(_x), y(_y), width(_width), height(_height),
    top_inset(_top_inset), left_inset(_left_inset), bottom_inset(_bottom_inset), right_inset(_right_inset),
    string_data(_string_data) {}

  float x;
  float y;
  float width;
  float height;
  float top_inset;
  float left_inset;
  float bottom_inset;
  float right_inset;
  const void* string_data;
};

struct BatchDidFinishCommand : public UICommand {
public:
  BatchDidFinishCommand(UICommandID _id, UICommandType _type, LayoutReason _reason)
  : UICommand(_id, _type, 0), reason(_reason) {}

  LayoutReason reason;
};

struct CreateKeyframesAnimationCommand : public UICommand {
 public:
  CreateKeyframesAnimationCommand(UICommandID _id,
                                  const PropValueType::Array& _tags,
                                  const PropValueType::Array& _keyframes,
                                  int _duration,
                                  const std::function<void()>& _callback)
      : UICommand(_id, UICommandType::CreateKeyframesAnimation, 0),
        tags(_tags),
        keyframes(_keyframes),
        duration(_duration),
        callback(_callback) {}
  PropValueType::Array tags;
  PropValueType::Array keyframes;
  int duration;
  std::function<void()> callback;
};

struct ClearKeyframesAnimationCommand : public UICommand {
public:
  ClearKeyframesAnimationCommand(UICommandID _id,
                                 const PropValueType::Array& _tags,
                                 const PropValue& _options,
                                 const std::function<void()>& _callback)
  : UICommand(_id, UICommandType::ClearKeyframesAnimation, 0),
  tags(_tags),
  options(_options),
  callback(_callback) {}
  PropValueType::Array tags;
  PropValue options;
  std::function<void()> callback;
};

struct FlushUITasksCommand : public UICommand {
  FlushUITasksCommand(UICommandID _id, UICommandType _type) : UICommand(_id, _type, 0) {}
};

}; // namespace blink::mt

#endif /* types_def_h */
