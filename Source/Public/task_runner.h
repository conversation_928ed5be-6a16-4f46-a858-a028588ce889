//
//  task_runner.h
//  MSCRenderer
//
//  Created by qinqiwei02 on 2025/5/10.
//

#include <functional>
#include <limits>

#include "types_def.h"

#ifndef LOGIC_TASK_RUNNER_H
#define LOGIC_TASK_RUNNER_H

namespace msc {

// For communication between the logic(js) and the renderer(blink),
// we have code architecture (in iOS) like this:

//     msc-native-renderer-ios
//     |(ref)            |(ref)
//     v                 v
//     msc(js thread)    msc-renderer(blink thread)

// In msc-native-renderer-ios's MSCMPUIManagerer::setup, we wrap
// msc's MSCService::enqueueExecution as a runner, and set into
// msc-renderer's NativeDOM. Then later, in the renderer, we could
// post task into the logic(i.e. service or js thread).

// And here is the wrapper of the task runner.
class TaskRunner {
 public:
  using Task = std::function<void()>;
  using Runner = std::function<bool(Task *)>;

 private:
  Runner runner_;

 public:
  // thread unsafe
  void SetRunner(Runner &&runner) { this->runner_ = std::move(runner); }
  bool PostTask(Task *task) {
    if (this->runner_) {
      return this->runner_(task);
    } else {
      return false;
    }
  }
};

struct JSCallbackInfo {
 public:
  unsigned callback_index_{std::numeric_limits<unsigned>::max() /*GenerateId()*/};
  int node_id_{-1};
  bool is_bound_to_node_{false};  // false if bound to the document
  bool is_once_{true};            // true if the js callback should be released after
                                  // being called once
  bool is_valid{true};

  enum Type {
    None,
    CreateKeyframesAnimation,
    ClearKeyframesAnimation,
    QueryEnhanced,
    IntersectionObserver,
  };
  Type type{};
};

class JSCallable;

struct JSCallback {
 public:
  JSCallback(std::unique_ptr<JSCallable> callable) : callable_{std::move(callable)} {}
  virtual ~JSCallback() = default;

  static unsigned GenerateId() {
    static unsigned id{};
    return ++id;
  }
  std::unique_ptr<JSCallable> callable_;
};

namespace native_dom {
class Element;
};

struct JSCallable {
 public:
  using Setter = std::function<void(JSCallable *, native_dom::Element *)>;
  enum class API {
    Simple,  // no parameters
    QueryEnhanced,
    IntersectionObserver
  };
  virtual ~JSCallable() = default;
  virtual void call() {}

  API api() { return api_; }

 protected:
  API api_{API::Simple};
};

struct JSCallbackForSimple : public JSCallable {
 public:
  using SimpleCallback = std::function<void()>;

 public:
  JSCallbackForSimple(SimpleCallback callback) : callback_{std::move(callback)} {}
  virtual void call() override { callback_(); }
  SimpleCallback callback_;
};

struct JSCallbackForQueryEnhanced : public JSCallable {
 public:
  JSCallbackForQueryEnhanced(blink::mt::QueryEnhancedCallback callback) : callback_{std::move(callback)} {
    api_ = JSCallable::API::QueryEnhanced;
  }
  virtual void call() override { callback_(std::move(entries_), element_); }
  native_dom::Element *element_{};
  blink::mt::QueryEnhancedEntries entries_;
  blink::mt::QueryEnhancedCallback callback_;
};

struct JSCallbackForIntersectionObserver : public JSCallable {
 public:
  JSCallbackForIntersectionObserver(blink::mt::IntersectionObserverCallback callback) : callback_{std::move(callback)} {
    api_ = JSCallable::API::IntersectionObserver;
  }
  virtual void call() override { callback_(std::move(entries_)); }
  std::vector<blink::mt::IntersectionEntryData> entries_;
  blink::mt::IntersectionObserverCallback callback_;
};

}  // namespace msc

#endif  // LOGIC_TASK_RUNNER_H
