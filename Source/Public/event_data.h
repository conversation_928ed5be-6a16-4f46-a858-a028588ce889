//
//  event_data.hpp
//  MSCRenderer
//
//  Created by ji<PERSON><PERSON> <PERSON><PERSON> on 2025/7/1.
//

#ifndef event_data_h
#define event_data_h

#include "touch.h"
#include <string>
#include <vector>
#include <chrono>
#include "props.h"

namespace msc {
namespace native_dom {

class EventData {
public:
  enum EventCategory {
    DEFAULT = 0,
    TOUCH = 1,
    TAP = 2,
    COMPONENT = 3
  };
  
  EventData(EventCategory event_category) :
    category_(event_category),
    timestamp_(std::chrono::system_clock::now()) {
  }
  
  EventCategory GetEventCategory() {
    return category_;
  }
  
  const std::string &GetEventName() {
    return event_name_;
  }
  
  void SetEventName(const std::string &event_name) {
    event_name_ = event_name;
  }
  
  void SetDetail(const std::shared_ptr<const blink::mt::Props> &detail) {
    detail_ = detail;
  }
  
  std::shared_ptr<const blink::mt::Pro<PERSON>> &GetDetail() {
    return detail_;
  }
  
  std::chrono::time_point<std::chrono::system_clock> GetTimeStamp() {
    return timestamp_;
  }
  
  void SetTouches(std::vector<Touch> &&touches) {
    touches_ = std::move(touches);
  }
  
  std::vector<Touch> &GetTouches() {
    return touches_;
  }
  
  void SetChangedTouches(std::vector<Touch> &&touches) {
    changed_touches_ = std::move(touches);
  }
  
  std::vector<Touch> &GetChangedTouches() {
    return changed_touches_;
  }
  
private:
  EventCategory category_;
  std::string event_name_;
  
  std::chrono::time_point<std::chrono::system_clock> timestamp_;
  std::shared_ptr<const blink::mt::Props> detail_;
  
  std::vector<Touch> touches_;
  std::vector<Touch> changed_touches_;
};

}
}

#endif /* event_data_hpp */
