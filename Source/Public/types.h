//
//  types.h
//  MSCRenderer
//
//  Created by Admin on 2024/11/8.
//

#ifndef types_h
#define types_h

typedef enum MSCBorderStyle {
  MSCBorderStyleUnset,
  MSCBorderStyleSolid,
  MSCBorderStyleDotted,
  MSCBorderStyleDashed,
} MSCBorderStyle;

typedef enum MSCPointerEvents {
  MSCPointerEventsUnspecified = 0, // Default
  MSCPointerEventsNone,
  MSCPointerEventsBoxNone,
  MSCPointerEventsBoxOnly,
} MSCPointerEvents;

#endif /* types_h */
