//
//  device.h
//  MSCRenderer
//
//  Created by <PERSON><PERSON> on 2024/9/24.
//

#ifndef device_h
#define device_h

namespace blink::mt {

class Device {
public:
  static void initScreenData(int32_t width, int32_t height, float_t scale) {
    screen_width_ = width;
    screen_height_ = height;
    screen_scale_ = scale;
  };

  static float screenScale() { return screen_scale_; }

  static float gridAlignedValue(float value, bool is_border = false) {
    if (is_border) {
      return floorf(floorf(value * screen_scale_) / screen_scale_ * 100) / 100.0;
    } else {
      return floorf(ceilf(value * screen_scale_) / screen_scale_ * 100) / 100.0;
    }
  };

  static int32_t screenWidth() { return screen_width_; }

  static int32_t screenHeight() { return screen_height_; }

private:
  static inline float screen_scale_ = 1;
  static inline int32_t screen_width_ = 0;
  static inline int32_t screen_height_ = 0;
};

}; // namespace blink::mt

#endif /* device_h */
