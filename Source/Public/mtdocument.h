//
//  mtdocument.h
//  MSCRenderer
//
//  Created by <PERSON><PERSON> on 2024/9/10.
//
#pragma once

#ifndef mtdocument_h
#define mtdocument_h

#include "config.h"
#include "types_def.h"

#include <string>
#include <functional>
#include <memory>
#include <unordered_map>
#include "task_runner.h"
#include "event_data.h"

namespace msc {
namespace native_dom {
  class DocumentRegistry;
  class Event;
}
}

#ifndef __APPLE__
#include <flatbuffers/flatbuffers.h>
#endif

#include "TraceRecorder.h"

namespace blink {

class Document;
class Node;
class Element;
class BlockLayoutAlgorithm;

}; // namespace blink

namespace trace {
class TraceRecorder;
}; // namespace trace

namespace blink::mt {

class MTDocumentData;
class TextMeasurer;
class RenderNode;
class UICommandBuffer;

class MTDocument : public enable_shared_from_this<MTDocument> {
public:
  static std::shared_ptr<MTDocument> Create(const Config&& config);

  MTDocument() = delete;

  ~MTDocument();

  // 必须在setup之前调用，不通过setup进行设置是为了方便清理horn开关
  void enableNativeDom(bool enable_native_dom);

  void setup(const std::shared_ptr<msc::native_dom::DocumentRegistry> &documentRegistry, int pageId);

  void SetPageId(int page_id);
  int pageId() {
    return pageId_;
  }

  void destroy();

  int perfId() const;

  Size& size() const;

  void setSize(Size&& size);

  void addCSS(const std::string&& css);

  void addCSSFile(const std::string&& css);

  void createNode(Tag tag,
                  const std::string&& view_name,
                  Tag root_tag,
                  const std::shared_ptr<const Props>& props);

  void updateNode(Tag tag,
                  const std::string&& view_name,
                  const std::shared_ptr<const Props>& props);

  void setWidthHeightFix(Tag tag, bool width_fix, bool height_fix, float width, float height);

  void setChildren(Tag parent_tag, const std::shared_ptr<const vector<Tag>>& child_tags);

  void removeNode(Tag tag);

  struct ChildrenChanges {
    std::vector<Tag> move_from_indices;
    std::vector<Tag> move_to_indices;
    std::vector<Tag> add_child_msc_tags;
    std::vector<Tag> add_at_indices;
    std::vector<Tag> remove_at_indices;
  };
  void manageChildren(Tag parent_tag, const std::shared_ptr<const ChildrenChanges>& changes);

  Element* getElementSync(Tag tag);

  Element* queryElementSync(Tag tag, const std::string& selectors);

  const Rect getClientRectSync(Tag tag);

  void QuerySelector(const std::string& selectors,
                     QuerySelectorCallback callback_c);

  void QuerySelectors(const std::string& selectors,
                      QuerySelectorsCallback callback_c);

  void layoutRoot(LayoutReason reason);

  void setUICommandBufferCallback(UICommandBufferCallback callback);

  /// 主动触发清空 UI 任务队列，如果没有主动清空则会在 BDC 之后统一清空
  void FlushUITasksIfNeeded();

  // TraceRecorder相关方法
  void setTraceRecorderPtr(long traceRecorderPtr);
  trace::TraceRecorder* getTraceRecorder() const;

  /// 执行脚本
  void evaluateScript(const std::string& script, const std::string& source);

  /// 设置加载 JS 脚本的回调
  void WXSSetLoadScriptCallback(std::function<void(MTDocument* document, const std::string&)> callback);

  /// 设置向逻辑 JS 引擎进行消息转发的回调
  void WXSSetTransportCallback(std::function<void(const std::string&)> callback);

  /// 加载外部脚本
  void WXSLoadScript(const std::string& script_name);

  /// 传递 WXS 事件
  void WXSTriggerEvent(const std::string& name, Tag target_tag, const std::shared_ptr<const Props>& event);

  /// 传递 WXS 属性变化事件
  void WXSTriggerPropChangeEvent(const std::string& prop_name, Tag target_tag, const std::shared_ptr<const Props>& args);

  /// 执行 transport
  void WXSTrasport(const std::string& args);

#ifndef __APPLE__
  void performBlockOnBlinkThread(std::function<void()> block);
  std::pair<uint8_t*, size_t> generateResult(flatbuffers::FlatBufferBuilder &builder,
                                                   const blink::mt::UICommands &buffer);
  void setMSINames(std::vector<std::string>&& names);
#endif

  void fireEvent(int elemId, const std::shared_ptr<msc::native_dom::EventData> &event);

  void QueryEnhanced(QueryEnhancedParams&& params,
                     msc::JSCallbackInfo callback_info);
  void CreateIntersectionObserver(CreateIntersectionObserverParams&& params,
                                  msc::JSCallbackInfo callback_info);
  void IntersectionObserverObserve(int intersection_observer_id, int tag);
  void CreateKeyframesAnimationEnhanced(
      const std::shared_ptr<blink::mt::AnimationProperties>& properties,
      msc::JSCallbackInfo callback_info);
  void ClearKeyframesAnimationEnhanced(
      const std::shared_ptr<blink::mt::ClearAnimationProperties>& properties,
      msc::JSCallbackInfo callback_info);

  // for posting to logic task runner, i.e. the js thread
  void SetLogicTaskRunner(msc::TaskRunner::Runner &&runner) {
    this->logic_task_runner_.SetRunner(std::move(runner));
  }
  bool PostTaskToLogicTaskRunner(msc::TaskRunner::Task* task) {
      return this->logic_task_runner_.PostTask(task);
  }

  void SetMscScrollViewContentOffset(double x, double y, long tag);

  class UICommandBuffer& uiCommandBuffer() const;

private:
  friend class blink::mt::RenderNode;
  friend class blink::BlockLayoutAlgorithm;

  MTDocumentData* data_;
  trace::TraceRecorder* trace_recorder_ptr_;  // TraceRecorder指针

private:
  MTDocument(const Config&& config);

  // i.e. the js thread
  msc::TaskRunner logic_task_runner_;

  const Config& config() const;
  class TextMeasurer& textMeasurer() const;

  void addCSSInternal(const std::string &css);
  Node* getNode(Tag tag);
  Node* getChildNode(Node *parent_node, int index);
  void addNodeInternal(Tag tag, Node *node);
  void removeNodeInternal(Tag tag, vector<int>& removed_tags);
  void preCalculateText(Element* element);

  void updateRelatedAttributesForProps(const std::shared_ptr<const Props>& props,
                                       blink::Element *element, Tag tag);

  void PostEventToService(int elemId, const std::shared_ptr<msc::native_dom::Event> &event);
  void PostCallbackToService(const msc::JSCallbackInfo& callback_info,
                             msc::JSCallable::Setter setter);

  void InterceptEventInBlinkThread(int elemId, const std::shared_ptr<msc::native_dom::Event> &event);

  void DestroyDOMDocument();
  
  int pageId_ = 0;
  bool root_view_created_ = false;
  bool enable_native_dom_ = false;
  std::weak_ptr<msc::native_dom::DocumentRegistry> document_registry_;

  void loadWXSEngine();
  bool WXSTriggerEventInternal(const std::string& action,
                               Element* target,
                               std::string event_func_name,
                               const std::shared_ptr<const Props>& args);
  Element* findOwner(Element* element);
};

}; // namespace blink::mt

#endif
