//
//  trace_scope.h
//  MSCRenderer
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/7/17.
//

#ifndef trace_scope_h
#define trace_scope_h

#include <string>
#include "TraceRecorder.h"

namespace trace {
class TraceRecorder;
enum class TraceLevel;
}; // namespace trace

namespace blink::mt {

// TraceRecorder RAII 包装类
class TraceScope {
public:
  TraceScope(trace::TraceRecorder* recorder, const std::string& name, trace::TraceLevel level)
    : recorder_(recorder), name_(name) {
    if (recorder_) {
      recorder_->beginTrace(name_, level);
    }
  }

  ~TraceScope() {
    if (recorder_) {
      recorder_->endTrace(name_);
    }
  }

  // 禁止拷贝和移动
  TraceScope(const TraceScope&) = delete;
  TraceScope& operator=(const TraceScope&) = delete;
  TraceScope(TraceScope&&) = delete;
  TraceScope& operator=(TraceScope&&) = delete;

private:
  trace::TraceRecorder* recorder_;
  std::string name_;
};

}; // namespace blink::mt

// 宏定义：RAII 方式的 trace（推荐使用）
// 使用方式：
// {
//   TRACE_SCOPE(document, "operation_name", trace::TraceLevel::P1);
//   // 在这个大括号内的代码会被自动 starttrace
//   // 当离开大括号时会自动调用 endTrace
// }
#define TRACE_SCOPE(document, name, level) \
  blink::mt::TraceScope trace_scope_##__LINE__((document)->getTraceRecorder(), (name), (level))

// 宏定义：手动 begin/end 方式的 trace（灵活度更高）
// 使用方式：
// TRACE_BEGIN(document, "operation_name", trace::TraceLevel::P1);
// // 你的代码
// TRACE_END(document, "operation_name");
#define TRACE_BEGIN(document, name, level) \
  do { \
    if ((document)->getTraceRecorder()) { \
      (document)->getTraceRecorder()->beginTrace((name), (level)); \
    } \
  } while(0)

#define TRACE_END(document, name) \
  do { \
    if ((document)->getTraceRecorder()) { \
      (document)->getTraceRecorder()->endTrace((name)); \
    } \
  } while(0)

#endif