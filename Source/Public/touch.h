//
// Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/5/22.
//

#ifndef MSC_ANDROID_TOUCH_H
#define MSC_ANDROID_TOUCH_H

namespace msc {
namespace native_dom {

class EventTarget;

class Touch {
public:
    Touch(int identifier,
          double client_x,
          double client_y,
          double page_x,
          double page_y,
          EventTarget *target,
          double force)
            : identifier_(identifier),
              client_x_(client_x),
              client_y_(client_y),
              page_x_(page_x),
              page_y_(page_y),
              target_(target),
              force_(force) {}

    ~Touch() = default;

    inline double GetClientX() const { return client_x_; }
    inline double GetClientY() const { return client_y_; }
    inline double GetPageX() const { return page_x_; }
    inline double GetPageY() const { return page_y_; }
    inline EventTarget* GetTarget() const { return target_; }
    inline int GetIdentifier() const { return identifier_; }
    inline double GetForce() const { return force_; }

private:
    int identifier_;
    double client_x_;
    double client_y_;
    double page_x_;
    double page_y_;
    EventTarget* target_;
    double force_;
};

} // native_dom
} // msc

#endif //MSC_ANDROID_TOUCH_H
