//
// Created by <PERSON><PERSON><PERSON><PERSON> on 2025/2/13.
//

#ifndef __APPLE__

#ifndef RENDERER_TEXT_MEASURE_ANDROID_H
#define RENDERER_TEXT_MEASURE_ANDROID_H

#include "third_party/blink/renderer/core/layout/layout_box.h"

namespace blink::mt {

    struct TextItem {
        int tag;
        int parent_tag;
        std::string text;
        std::string type;
        std::string font_family;
        std::string font_style;
        std::string text_align;
        std::string text_overflow;
        std::string ellipsize_mode;
        int font_size;
        int font_weight;
        int color;
        float line_height;
        int number_of_lines;
    };

    struct TextInfo {
        int msc_tag;
        std::string document_key;
        std::string text_data;
        std::vector<TextItem> text_items;

        virtual ~TextInfo() = default;
    };

}; // namespace blink::mt

#endif //RENDERER_TEXT_MEASURE_ANDROID_H

#endif
