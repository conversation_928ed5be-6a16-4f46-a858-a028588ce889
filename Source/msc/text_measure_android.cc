//
// Created by <PERSON><PERSON><PERSON><PERSON> on 2025/2/13.
//

#ifndef __APPLE__

#include "text_measure_android.h"
#include "third_party/blink/renderer/core/layout/layout_text.h"
#include "third_party/blink/renderer/core/layout/constraint_space_builder.h"
#include "third_party/blink/renderer/core/layout/block_node.h"
#include <layout_inline.h>
#include "MSCRenderer.h"
#include "third_party/blink/renderer/core/html/html_element.h"

#include "style_convertor_helper.h"
#include "layout_utils.h"
#include "Text_generated.h"
#include "text_measure.h"

using namespace com::meituan::android::msc::renderer::generated;

namespace blink::mt {
    namespace {
        void CreateTextAttributes(TextItem &text_item, const ComputedStyle &style) {
            text_item.font_family = style.GetFontDescription().Family().FamilyName().Utf8();
            int font_style = style.GetFontDescription().Style();
            text_item.font_style = font_style != kNormalSlopeValue ? "italic" : "normal";
            text_item.text_align = mt::StyleConvertor::Convert(style.GetTextAlign());

            std::string lineBreakMode;
            std::string ellipsizeMode;
            if (style.TextOverflow() == ETextOverflow::kClip) {
                text_item.text_overflow = "clip";
                text_item.ellipsize_mode = "clip";
            } else if (style.TextOverflow() == ETextOverflow::kEllipsis) {
                text_item.text_overflow = "ellipsis";
                text_item.ellipsize_mode = "tail";
            }
            text_item.font_size = style.FontSize();
            text_item.font_weight = style.GetFontDescription().Weight().RawValue();
            text_item.color = (int) mt::StyleConvertor::ConvertColorToInt32(style.GetCurrentColor());
            auto line_height = style.ComputedLineHeight();
            if (!isnan(line_height)) {
                text_item.line_height = line_height;
            }
            if (style.BoxOrient() == EBoxOrient::kVertical &&
                style.OverflowX() == EOverflow::kHidden &&
                style.OverflowY() == EOverflow::kHidden) {
                int line = style.WebkitLineClamp();
                text_item.number_of_lines = line;
            } else {
                text_item.number_of_lines = 0;
            }
            if (style.WhiteSpace() == EWhiteSpace::kNowrap) {
                text_item.number_of_lines = 1;
            }
        }

        void generateResult(flatbuffers::FlatBufferBuilder &builder, const TextInfo& text_info) {
            std::vector<flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Text>> textInfoList;
            for (auto &text_item: text_info.text_items) {
                flatbuffers::Offset<flatbuffers::String> textBuf = builder.CreateString(text_item.text);
                flatbuffers::Offset<flatbuffers::String> typeBuf = builder.CreateString(text_item.type);
                if (text_item.type == "raw") {
                    TextBuilder textBuilder(builder);
                    textBuilder.add_tag(text_item.tag);
                    textBuilder.add_parent_tag(text_item.parent_tag);
                    textBuilder.add_text(textBuf);
                    textBuilder.add_type(typeBuf);
                    textInfoList.push_back(textBuilder.Finish());
                } else {
                    flatbuffers::Offset<flatbuffers::String> fontFamilyBuf = builder.CreateString(text_item.font_family);
                    flatbuffers::Offset<flatbuffers::String> fontStyleBuf = builder.CreateString(text_item.font_style);
                    flatbuffers::Offset<flatbuffers::String> textAlignBuf = builder.CreateString(text_item.text_align);
                    flatbuffers::Offset<flatbuffers::String> textOverflowBuf = builder.CreateString(text_item.text_overflow);
                    flatbuffers::Offset<flatbuffers::String> ellipsizeModeBuf = builder.CreateString(text_item.ellipsize_mode);
                    TextBuilder textBuilder(builder);
                    textBuilder.add_tag(text_item.tag);
                    textBuilder.add_parent_tag(text_item.parent_tag);
                    textBuilder.add_type(typeBuf);
                    textBuilder.add_font_family(fontFamilyBuf);
                    textBuilder.add_font_style(fontStyleBuf);
                    textBuilder.add_text_align(textAlignBuf);
                    textBuilder.add_text_overflow(textOverflowBuf);
                    textBuilder.add_ellipsize_mode(ellipsizeModeBuf);
                    textBuilder.add_font_size(text_item.font_size);
                    textBuilder.add_font_weight(text_item.font_weight);
                    textBuilder.add_color(text_item.color);
                    textBuilder.add_line_height(text_item.line_height);
                    textBuilder.add_number_of_lines(text_item.number_of_lines);
                    textInfoList.push_back(textBuilder.Finish());
                }
            }
            auto texts_offset = builder.CreateVector(textInfoList);
            builder.Finish(CreateMeasureTextInfo(builder, texts_offset));
        }
    }

    const void* RetainTextData(const void *text_data) {
        if (text_data) {
            return text_data;
        }
        return nullptr;
    }

    const void *CreateTextData(LayoutBox *box, LayoutUnit constraint_width) {
        flatbuffers::FlatBufferBuilder builder(1024);
        auto *text_info = new TextInfo();
        std::vector<TextItem> text_items;

        mt::TraverseLayoutTree(box, [&](const mt::LayoutTreeItem &item, bool* skip_children) {
            if (IsA<LayoutView>(item.node)) {
                return;
            }
            TextItem text_item;
            LayoutObject *layout_object = item.node;
            if (IsA<LayoutInline>(layout_object) || IsA<LayoutText>(layout_object)) {
                layout_object->ClearNeedsLayout();
            }
            text_item.tag = layout_object->GetNode()->getMscTag();
            auto parent_layout_object = layout_object->Parent();
            if (parent_layout_object) {
                text_item.parent_tag = parent_layout_object->GetNode()->getMscTag();
            }
            if (IsA<LayoutText>(layout_object)) {
                auto layout_text = To<LayoutText>(layout_object);
                auto &text = layout_text->TransformedText();
                text_item.text = text.Utf8();
                text_item.type = "raw";
                // 追加到字符串后面
            } else if (IsA<LayoutBlock>(layout_object)) {
                text_item.type = "text";
                CreateTextAttributes(text_item, layout_object->StyleRef());
            } else if (IsA<LayoutInline>(layout_object)) {
                text_item.type = "text";
                CreateTextAttributes(text_item, layout_object->StyleRef());
            }
            text_items.push_back(text_item);
        });
        text_info->text_items = text_items;
        auto elem = To<HTMLElement>(box->GetNode());
        text_info->msc_tag = box->GetNode()->getMscTag();
        text_info->document_key = elem->getProp("documentKey").stringValue();
        return static_cast<void *>(text_info);
    }

    void ReleaseTextData(const void *text_data) {
    }

    void CalculateTextSize(LayoutBox *box, LayoutUnit constraint_width, const void *text_data, LayoutUnit *out_width, LayoutUnit *out_height){
        if (!text_data) {
            *out_width = LayoutUnit();
            *out_height = LayoutUnit();
            return;
        }
        auto text_info = static_cast<const TextInfo *>(text_data);

        flatbuffers::FlatBufferBuilder builder(1024);
        generateResult(builder, *text_info);

        uint8_t* buffer_ptr = builder.GetBufferPointer();
        size_t buffer_size = builder.GetSize();

        std::vector<float> result = measureText(text_info->document_key, text_info->msc_tag,
                                                constraint_width != kIndefiniteSize
                                                ? constraint_width.ToDouble()
                                                : FLT_MAX, 0, buffer_ptr, buffer_size);
        auto vec_result = std::make_shared<std::vector<float>>(result);
        *out_width = LayoutUnit::FromFloatCeil((*vec_result)[0]);
        *out_height = LayoutUnit::FromFloatCeil((*vec_result)[1]);
    }

    void UpdateTextFinalSize(const void* text_data, float width, float height) {
        
    }

    void CalculateTextAttachmentFrame(const void *string_data,
                                      Tag attachment_tag,
                                      float *out_x,
                                      float *out_y,
                                      float *out_width,
                                      float *out_height,
                                      bool *out_hidden) {

    }
}

#endif
