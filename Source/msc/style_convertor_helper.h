//
//  StyleConvertor.hpp
//  MSCRenderer
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2024/11/4.
//

#pragma once

#import "mtdocument.h"
#ifdef __APPLE__
#import "types.h"
#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#else
#include "NativeLog.h"
#include <flatbuffers/flatbuffers.h>
#include "Command_generated.h"
#endif

#include "third_party/blink/renderer/platform/graphics/color.h"
#include "third_party/blink/renderer/platform/geometry/length.h"
#include "third_party/blink/renderer/core/style/computed_style.h"
#include "third_party/blink/renderer/core/style/shadow_list.h"
#include "third_party/blink/renderer/core/style/computed_style_base_constants.h"
#include "third_party/blink/renderer/platform/transforms/matrix_3d_transform_operation.h"
#include "third_party/blink/renderer/platform/transforms/matrix_transform_operation.h"
#include "third_party/blink/renderer/platform/transforms/perspective_transform_operation.h"
#include "third_party/blink/renderer/platform/transforms/skew_transform_operation.h"

using namespace blink;
#ifndef __APPLE__
using namespace com::meituan::android::msc::renderer::generated;
#endif

namespace blink::mt {

class StyleConvertor {
public:
#ifdef __APPLE__
  static inline UIColor *Convert(const Color &color) {
    auto skColor = color.toSkColor4f();
    return [UIColor colorWithRed:skColor.fR green:skColor.fG blue:skColor.fB alpha:skColor.fA];
  }
#endif

  static inline std::uint32_t ConvertColorToInt32(const Color& color)
  {
    auto skColor = color.toSkColor4f();
      int r1 = skColor.fR *255;
      int g1 = skColor.fG *255;
      int b1 = skColor.fB *255;
      int a1 = skColor.fA *255;
      std::int32_t a2= a1 <<24;
      std::int32_t r2= r1 << 16;
      std::int32_t g2= g1 << 8;
      std::int32_t b2= b1;

      return (r2+g2+b2+a2)&(0xffffffff);
  }
#ifdef __APPLE__
  static inline float ConvertLengthToFloat(const Length &length, CGFloat refLength) {
    switch (length.GetType()) {
      case Length::Type::kFixed:
        return length.Pixels();
      case Length::Type::kPercent:
        return length.Percent() / 100.0 * refLength;
      default:
        return 0;
    }
  }
#else
  static inline float ConvertLengthToFloat(const Length &length, float density, const std::float_t refLength) {
    switch (length.GetType()) {
      case Length::Type::kFixed:
        return length.Pixels() * density;
      case Length::Type::kPercent:
        return length.Percent() / 100.0 * refLength;
      default:
        return 0;
    }
 }
#endif

//  static inline float ConvertLengthToFloat(const Length& length) {
//    switch (length.GetType()) {
//      case Length::Type::kFixed:
//        return length.Pixels();
//      case Length::Type::kPercent:
//        return length.Percent() / 100.0;
//      default:
//        return 0;
//    }
//    return 0;
//  }

  static inline std::string Convert(const Length &length) {
     switch (length.GetType()) {
       case Length::Type::kAuto:
         return "auto";
       case Length::Type::kContent:
         return "content";
       case Length::Type::kNone:
         return "undefined";
       case Length::Type::kFixed:
         return std::to_string(length.Pixels()) + "px";
       case Length::Type::kPercent:
         return std::to_string(length.Percent()) + "%";
       default:
         return "auto";
     }
  }

  static inline std::string Convert(EFillRepeat repeat) {
    switch (repeat) {
      case EFillRepeat::kRepeatFill: return "repeat"; break;
      case EFillRepeat::kNoRepeatFill: return "no-repeat"; break;
      case EFillRepeat::kRoundFill: return "round"; break;
      case EFillRepeat::kSpaceFill: return "space"; break;
    }
    return "";
  }

#ifdef __APPLE__
  static inline MSCBorderStyle Convert(EBorderStyle borderStyle) {
    switch (borderStyle) {
      case EBorderStyle::kDotted: return MSCBorderStyleDotted;
      case EBorderStyle::kDashed: return MSCBorderStyleDashed;
      case EBorderStyle::kSolid: return MSCBorderStyleSolid;
      default: return MSCBorderStyleSolid;
    }
//    switch (borderStyle) {
//      case EBorderStyle::kNone: return @"none"; break;
//      case EBorderStyle::kHidden: return @"hidden"; break;
//      case EBorderStyle::kInset: return @"inset"; break;
//      case EBorderStyle::kGroove: return @"groove"; break;
//      case EBorderStyle::kOutset: return @"outset"; break;
//      case EBorderStyle::kRidge: return @"ridge"; break;
//      case EBorderStyle::kDotted: return @"dotted"; break;
//      case EBorderStyle::kDashed: return @"dashed"; break;
//      case EBorderStyle::kSolid: return @"solid"; break;
//      case EBorderStyle::kDouble: return @"double"; break;
//    }
//    return @"";
  }

  static inline NSDictionary *Convert(scoped_refptr<Gradient> backgroundGradient) {
    NSMutableDictionary *gradientDic = nil;
    if (backgroundGradient && backgroundGradient->GetType() == blink::Gradient::Type::kLinear) {
      gradientDic = [[NSMutableDictionary alloc] init];
      [gradientDic setValue:@"linear-gradient" forKey:@"type"];
      auto linearGradient = static_cast<LinearGradient *>(backgroundGradient.get());
      auto p0 = linearGradient->getP0();
      auto p1 = linearGradient->getP1();

      NSArray<NSNumber *> *firstPoint = @[@(p0.x()/100), @(p0.y()/100)];
      NSArray<NSNumber *> *secondPoint = @[@(p1.x()/100), @(p1.y()/100)];
      NSMutableArray *offsets = [[NSMutableArray alloc] init];
      NSMutableArray *colors = [[NSMutableArray alloc] init];

      auto stops = backgroundGradient->GetStops();
      for (auto stop : stops) {
          [offsets addObject:@(stop.stop)];
          [colors addObject:@(StyleConvertor::ConvertColorToInt32(stop.color))];
      }
      [gradientDic setValue:firstPoint forKey:@"firstPoint"];
      [gradientDic setValue:secondPoint forKey:@"secondPoint"];
      [gradientDic setValue:offsets forKey:@"offsets"];
      [gradientDic setValue:colors forKey:@"colors"];
    }
    return gradientDic;
  }
#else
  static inline std::string Convert(const Length &length, float density) {
    switch (length.GetType()) {
        case Length::Type::kAuto:
            return "auto";
        case Length::Type::kContent:
            return "content";
        case Length::Type::kNone:
            return "undefined";
        case Length::Type::kFixed:
            return std::to_string(length.Pixels() * density) + "px";
        case Length::Type::kPercent:
            return std::to_string(length.Percent()) + "%";
        default:
            return "auto";
    }
  }

  static inline std::string Convert(EBorderStyle borderStyle) {
    switch (borderStyle) {
      case EBorderStyle::kDotted: return "dotted";
      case EBorderStyle::kDashed: return "dashed";
      case EBorderStyle::kSolid: return "solid";
      default: return "solid";
    }
  }

  static inline std::unordered_map<std::string, std::variant<std::vector<float>, std::vector<int>>> Convert(scoped_refptr<Gradient> backgroundGradient) {
    std::unordered_map<std::string, std::variant<std::vector<float>, std::vector<int>>> gradientDic;
    if (backgroundGradient && backgroundGradient->GetType() == blink::Gradient::Type::kLinear) {
      auto linearGradient = static_cast<LinearGradient *>(backgroundGradient.get());
      auto p0 = linearGradient->getP0();
      auto p1 = linearGradient->getP1();

      std::vector<float> firstPoint = {p0.x() / 100, p0.y() / 100};
      std::vector<float> secondPoint = {p1.x() / 100, p1.y() / 100};

      gradientDic["firstPoint"] = firstPoint;
      gradientDic["secondPoint"] = secondPoint;

//    LOGI("background_gradient %f %f %f %f ", secondPoint[0], secondPoint[1], p1.x() / 100, p1.y() / 100);
      std::vector<float> offsets;
      std::vector<int> colors;
      auto stops = backgroundGradient->GetStops();
      for (auto stop: stops) {
        offsets.push_back(stop.stop);
        colors.push_back(ConvertColorToInt32(stop.color));
//      LOGI("background_gradient %f %d ", stop.stop, ConvertColorToInt32(stop.color));
      }
      gradientDic["offsets"] = offsets;
      gradientDic["colors"] = colors;
    }
    return gradientDic;
  }
#endif

  static inline std::string Convert(const EFillSizeType &sizeType) {
    switch (sizeType) {
      case EFillSizeType::kContain: return "contain"; break;
      case EFillSizeType::kCover: return "cover"; break;
      case EFillSizeType::kSizeLength: return "size-length"; break;
      case EFillSizeType::kSizeNone: return "size-none"; break;
    }
    return "";
  }

#ifdef __APPLE__
  static inline NSDictionary *Convert(const blink::FillSize &fillSize) {
    NSMutableDictionary *fillSizeDict = [NSMutableDictionary dictionary];
    fillSizeDict[@"type"] = [NSString stringWithUTF8String:Convert(fillSize.type).c_str()];
    if (fillSize.type == EFillSizeType::kSizeLength) {
      std::string width = StyleConvertor::Convert(fillSize.size.Width());
      std::string height = StyleConvertor::Convert(fillSize.size.Height());
      fillSizeDict[@"size"] = @[
        [NSString stringWithUTF8String:width.c_str()],
        [NSString stringWithUTF8String:height.c_str()]
      ];
    }
    return fillSizeDict;
  }
#else
  static inline std::unordered_map<std::string, std::variant<std::string, std::vector<std::string>>> Convert(const FillSize &fillSize) {
    std::unordered_map<std::string, std::variant<std::string, std::vector<std::string>>> fillSizeDict;
    std::string sizeType = Convert(fillSize.type);
    fillSizeDict["type"] = sizeType;
    if (fillSize.type == EFillSizeType::kSizeLength) {
      std::vector<std::string> sizeVec;
      auto size = fillSize.size;
      sizeVec.push_back(StyleConvertor::Convert(size.Width()));
      sizeVec.push_back(StyleConvertor::Convert(size.Height()));
      fillSizeDict["size"] = sizeVec;
    }
    return fillSizeDict;
  }
#endif

  static inline float ConvertBorderRadius(const LengthSize &borderRadius) {
    //TODO: jz - 这里参照之前webkit抽取的实现，只支持fixed px
    if (borderRadius.Width().IsFixed()) {
      return borderRadius.Width().Pixels();
    }
    return 0;
  }

#ifdef __APPLE__
  static inline NSString *Convert(EBoxSizing boxSizing) {
    switch (boxSizing) {
      case EBoxSizing::kContentBox: return @"content-box"; break;
      case EBoxSizing::kBorderBox: return @"border-box"; break;
    }
    return @"";
  }
#else
  static inline std::string Convert(EBoxSizing boxSizing) {
    switch (boxSizing) {
      case EBoxSizing::kContentBox: return "content-box"; break;
      case EBoxSizing::kBorderBox: return "border-box"; break;
    }
    return "";
  }
#endif

  static inline std::string Convert(EOverflow overflow) {
    switch (overflow) {
      case EOverflow::kVisible: return "visible"; break;
      case EOverflow::kHidden: return "hidden"; break;
      case EOverflow::kScroll: return "scroll"; break;
        //        case Overflow::Auto: return "auto"; break;
        //        case Overflow::PagedX: return "paged-x"; break;
        //        case Overflow::PagedY: return "paged-y"; break;
        //        case Overflow::Clip: return "clip"; break;
      default: return "visible";
    }
  }

#ifdef __APPLE__
  static inline NSTextAlignment Convert(ETextAlign text_align) {
    switch (text_align) {
      case ETextAlign::kCenter:
        return NSTextAlignmentCenter;
      case ETextAlign::kRight:
        return NSTextAlignmentRight;
      case ETextAlign::kJustify:
        return NSTextAlignmentJustified;
      default:
        return NSTextAlignmentLeft;
    }
  }
#else
  static inline std::string Convert(ETextAlign text_align) {
    switch (text_align) {
      case ETextAlign::kCenter:
        return "center";
      case ETextAlign::kRight:
        return "right";
      case ETextAlign::kJustify:
        return "justify";
      default:
        return "left";
    }
  }
#endif

#ifdef __APPLE__
  static inline UIFontWeight ConvertFontWeight(FontSelectionValue font_weight) {
    if (font_weight >= kBlackWeightValue) {
      return UIFontWeightBlack;
    } else if (font_weight >= kExtraBoldWeightValue) {
      return UIFontWeightHeavy;
    } else if (font_weight >= kBoldWeightValue) {
      return UIFontWeightBold;
    } else if (font_weight >= kSemiBoldWeightValue) {
      return UIFontWeightSemibold;
    } else if (font_weight >= kMediumWeightValue) {
      return UIFontWeightMedium;
    } else if (font_weight >= kNormalWeightValue) {
      return UIFontWeightRegular;
    } else if (font_weight >= kLightWeightValue) {
      return UIFontWeightLight;
    } else if (font_weight >= kExtraLightWeightValue) {
      return UIFontWeightThin;
    } else if (font_weight >= kThinWeightValue) {
      return UIFontWeightUltraLight;
    }
    return UIFontWeightRegular;
  }

  static inline NSDictionary *Convert(ShadowList *shadowList, const ComputedStyle *style) {
    if (!shadowList) {
      return nil;
    }

    wtf_size_t shadowCount = shadowList->Shadows().size();
    for (wtf_size_t i = 0; i < shadowCount; ++i) {
      auto shadow = shadowList->Shadows()[i];

      UIColor *color = Convert(shadow.GetColor().Resolve(style->GetCurrentColor(), style->UsedColorScheme()));

      float x = shadow.X();
      float y = shadow.Y();
      float spread = shadow.Spread();
      float blur = shadow.Blur();
      auto style = shadow.Style();
      NSString *styleStr = @"";
      if (style == ShadowStyle::kNormal) {
        styleStr = @"normal";
      } else if (style == ShadowStyle::kInset) {
        styleStr = @"inset";
      }
      return @{
        @"x" : @(x),
        @"y" : @(y),
        @"spread" : @(spread),
        @"radius" : @(blur),
        @"color" : color ?: [UIColor lightTextColor],
        @"style" : styleStr,
        @"opacity": @(shadow.Opacity())
      };
    }

    return nil;
  }
  static inline MSCPointerEvents Convert(EPointerEvents pointerEvents) {
    // W3C与RN在pointerEvents的概念有较大出入，除了none之外，不能直接进行属性映射，目前仅支持none
    switch (pointerEvents) {
        case EPointerEvents::kNone:
            return MSCPointerEventsNone;
            break;
        default:
            return MSCPointerEventsUnspecified;
            break;
    }
  }
#else
  static inline std::string Convert(EPointerEvents pointerEvents) {
    // W3C与RN在pointerEvents的概念有较大出入，除了none之外，不能直接进行属性映射，目前仅支持none
    switch (pointerEvents) {
      case EPointerEvents::kNone:
        return "none";
      default:
        return "auto";
      }
    }

  static inline std::string Convert(TextDecorationLine line) {
    switch (line) {
      case TextDecorationLine::kNone: return "none";
      case TextDecorationLine::kUnderline: return "underline";
      case TextDecorationLine::kOverline: return "overline";
      case TextDecorationLine::kLineThrough: return "line-through";
      case TextDecorationLine::kBlink: return "blink";
      case TextDecorationLine::kSpellingError:
        break;
      case TextDecorationLine::kGrammarError:
        break;
    }
    return "";
  }

  static inline flatbuffers::Offset<::flatbuffers::Vector<flatbuffers::Offset<PropArray>>> ConvertBackgroundImage(flatbuffers::FlatBufferBuilder &builder, const std::string& background_image, scoped_refptr<Gradient> background_gradient) {
    std::vector<flatbuffers::Offset<PropArray>> resultArray;
    if (!background_image.empty()) {
      std::vector<flatbuffers::Offset<Prop>> backgroundImageVector;
      flatbuffers::Offset<flatbuffers::String> urlStr = builder.CreateString(background_image);
      flatbuffers::Offset<flatbuffers::String> typeStr = builder.CreateString("url");

      auto typeKey = builder.CreateString("type");
      auto typeValue = CreateStringValue(builder, typeStr);
      flatbuffers::Offset<Prop> colorProp = CreateProp(builder, typeKey, Value_StringValue, typeValue.Union());
      backgroundImageVector.push_back(colorProp);

      auto urlKey = builder.CreateString("value");
      auto urlValue = CreateStringValue(builder, urlStr);
      flatbuffers::Offset<Prop> urlProp = CreateProp(builder, urlKey, Value_StringValue, urlValue.Union());
      backgroundImageVector.push_back(urlProp);

      flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<Prop>>> backgroundImageVectorBuf = builder.CreateVector(backgroundImageVector);
      flatbuffers::Offset<PropArray> urlImageVectorBuf = CreatePropArray(builder, backgroundImageVectorBuf);
      resultArray.push_back(urlImageVectorBuf);
    }

    if (background_gradient != nullptr && background_gradient->GetType() == Gradient::Type::kLinear) {
      auto linearGradient = static_cast<LinearGradient *>(background_gradient.get());
      if (linearGradient != nullptr) {
        std::vector<flatbuffers::Offset<Prop>> gradientVector;
        auto stops = background_gradient->GetStops();
        auto p0 = linearGradient->getP0();
        auto p1 = linearGradient->getP1();
        std::vector<float> firstPoint = {p0.x() / 100, p0.y() / 100};
        std::vector<float> secondPoint = {p1.x() / 100, p1.y() / 100};
        flatbuffers::Offset <flatbuffers::String> typeStr = builder.CreateString("linear-gradient");
        std::vector<float> offsets;
        std::vector<int32_t> colors;
        std::vector<int32_t> isPercents; // FlatBuffers中bool用uint8表示
        for (auto stop: stops) {
          offsets.push_back(stop.stop);
          colors.push_back((int) StyleConvertor::ConvertColorToInt32(stop.color));
          isPercents.push_back(1);
        }
        auto typeKey = builder.CreateString("type");
        auto typeValue = CreateStringValue(builder, typeStr);
        flatbuffers::Offset<Prop> typeProp = CreateProp(builder, typeKey, Value_StringValue, typeValue.Union());
        gradientVector.push_back(typeProp);

        auto firstPointKey = builder.CreateString("firstPoint");
        auto firstPointValue = CreateFloatArrayDirect(builder, &firstPoint);
        flatbuffers::Offset<Prop> firstPointProp = CreateProp(builder, firstPointKey, Value_FloatArray, firstPointValue.Union());
        gradientVector.push_back(firstPointProp);

        auto secondPointKey = builder.CreateString("secondPoint");
        auto secondPointValue = CreateFloatArrayDirect(builder, &secondPoint);
        flatbuffers::Offset<Prop> secondPointProp = CreateProp(builder, secondPointKey, Value_FloatArray, secondPointValue.Union());
        gradientVector.push_back(secondPointProp);

        auto offsetsKey = builder.CreateString("offsets");
        auto offsetsValue = CreateFloatArrayDirect(builder, &offsets);
        flatbuffers::Offset<Prop> offsetsProp = CreateProp(builder, offsetsKey, Value_FloatArray, offsetsValue.Union());
        gradientVector.push_back(offsetsProp);

        auto colorsKey = builder.CreateString("colors");
        auto colorsValue = CreateIntArrayDirect(builder, &colors);
        flatbuffers::Offset<Prop> colorsProp = CreateProp(builder, colorsKey, Value_IntArray, colorsValue.Union());
        gradientVector.push_back(colorsProp);

        auto isPercentsKey = builder.CreateString("isPercents");
        auto isPercentsValue = CreateIntArrayDirect(builder, &isPercents);
        flatbuffers::Offset<Prop> isPercentsProp = CreateProp(builder, isPercentsKey, Value_IntArray, isPercentsValue.Union());
        gradientVector.push_back(isPercentsProp);

        flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<Prop>>> gradientVectorBuf = builder.CreateVector(gradientVector);
        flatbuffers::Offset<PropArray> gradientVectorPropArryBuf = CreatePropArray(builder, gradientVectorBuf);
        resultArray.push_back(gradientVectorPropArryBuf);
      }
    }
    if (resultArray.size() > 0) {
      flatbuffers::Offset<::flatbuffers::Vector<flatbuffers::Offset<PropArray>>> resultBuf = builder.CreateVector(resultArray);
      return resultBuf;
    }
    return 0;
  }

  static inline flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<PropArray>>> ConvertBackgroundSize(flatbuffers::FlatBufferBuilder &builder, const FillSize &background_size, float density) {
    // backgroundSize
    LengthSize defaultItemSize = LengthSize();
    LengthSize itemSize = background_size.size;
    EFillSizeType itemType = background_size.type;
    std::vector<flatbuffers::Offset<PropArray>> resultArray;

    if (itemSize != defaultItemSize || itemType != EFillSizeType::kSizeLength) {
      std::vector<flatbuffers::Offset<Prop>> backgroundSizeProps;
      std::string sizeType = StyleConvertor::Convert(itemType);
      flatbuffers::Offset<flatbuffers::String> typeStr = builder.CreateString(sizeType);
      flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> sizeArrayBuf = 0;
      if (itemType == EFillSizeType::kSizeLength) {
        flatbuffers::Offset<flatbuffers::String> widthStr = builder.CreateString(StyleConvertor::Convert(itemSize.Width(), density));
        flatbuffers::Offset<flatbuffers::String> heightStr = builder.CreateString(StyleConvertor::Convert(itemSize.Height(), density));
        std::vector<flatbuffers::Offset<flatbuffers::String>> sizeArray = {widthStr, heightStr};
        sizeArrayBuf = builder.CreateVector(sizeArray);
      }
      auto styleKey = builder.CreateString("type");
      auto styleValue = CreateStringValue(builder, typeStr);
      flatbuffers::Offset<Prop> colorProp = CreateProp(builder, styleKey, Value_StringValue, styleValue.Union());
      backgroundSizeProps.push_back(colorProp);

      auto sizeKey = builder.CreateString("size");
      auto sizeValue = CreateStringArray(builder, sizeArrayBuf);
      flatbuffers::Offset<Prop> sizeProp = CreateProp(builder, sizeKey, Value_StringArray, sizeValue.Union());
      backgroundSizeProps.push_back(sizeProp);
      flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<Prop>>> backgroundSizeVectorBuf = builder.CreateVector(backgroundSizeProps);
      flatbuffers::Offset<PropArray> urlImageVectorBuf = CreatePropArray(builder, backgroundSizeVectorBuf);
      resultArray.push_back(urlImageVectorBuf);
    }
    if (resultArray.empty()) {
      return 0;
    }
    flatbuffers::Offset<::flatbuffers::Vector<flatbuffers::Offset<PropArray>>> resultBuf = builder.CreateVector(resultArray);
    return resultBuf;
  }

  static inline flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<Prop>>> ConvertFbs(flatbuffers::FlatBufferBuilder& builder, ShadowList* shadowList, std::vector<Color> colors) {
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<Prop>>> boxShadowVectorBuf = 0;
    if (shadowList != nullptr && shadowList->Shadows().size() > 0 && colors.size() > 0) {
      std::vector<flatbuffers::Offset<Prop>> shadowProps;
      wtf_size_t shadowCount = shadowList->Shadows().size();
      auto shadow = shadowList->Shadows()[0];

      auto colorKey = builder.CreateString("color");
      int color = (int) StyleConvertor::ConvertColorToInt32(colors[0]);
      auto colorValue = CreateIntValue(builder, color);
      flatbuffers::Offset<Prop> colorProp = CreateProp(builder, colorKey, Value_IntValue, colorValue.Union());
      shadowProps.push_back(colorProp);

      flatbuffers::Offset<flatbuffers::String> xBuf = builder.CreateString(std::to_string(shadow.X()) + "px");
      auto xKey = builder.CreateString("x");
      auto xValue = CreateStringValue(builder, xBuf);
      flatbuffers::Offset<Prop> xProp = CreateProp(builder, xKey, Value_StringValue, xValue.Union());
      shadowProps.push_back(xProp);

      flatbuffers::Offset<flatbuffers::String> yBuf = builder.CreateString(std::to_string(shadow.Y()) + "px");
      auto yKey = builder.CreateString("y");
      auto yValue = CreateStringValue(builder, yBuf);
      flatbuffers::Offset<Prop> yProp = CreateProp(builder, yKey, Value_StringValue, yValue.Union());
      shadowProps.push_back(yProp);

      auto spreadKey = builder.CreateString("spread");
      auto spreadValue = CreateFloatValue(builder, shadow.Spread());
      flatbuffers::Offset<Prop> spreadProp = CreateProp(builder, spreadKey, Value_FloatValue, spreadValue.Union());
      shadowProps.push_back(spreadProp);

      auto radiusKey = builder.CreateString("radius");
      auto radiusValue = CreateFloatValue(builder, shadow.Blur());
      flatbuffers::Offset<Prop> radiusProp = CreateProp(builder, radiusKey, Value_FloatValue, radiusValue.Union());
      shadowProps.push_back(radiusProp);

      flatbuffers::Offset<flatbuffers::String> styleBuf = 0;
      auto styleKey = builder.CreateString("style");
      if (shadow.Style() == ShadowStyle::kNormal) {
        styleBuf = builder.CreateString("normal");
      } else if (shadow.Style() == ShadowStyle::kInset) {
        styleBuf = builder.CreateString("inset");
      }
      auto styleValue = CreateStringValue(builder, styleBuf);
      flatbuffers::Offset<Prop> styleProp = CreateProp(builder, styleKey, Value_StringValue, styleValue.Union());
      shadowProps.push_back(styleProp);

      auto opacityKey = builder.CreateString("opacity");
      auto opacityValue = CreateFloatValue(builder, shadow.Opacity());
      flatbuffers::Offset<Prop> opacityProp = CreateProp(builder, opacityKey, Value_FloatValue,
                                                               opacityValue.Union());
      shadowProps.push_back(opacityProp);
      boxShadowVectorBuf = builder.CreateVector(shadowProps);
    }
    return boxShadowVectorBuf;
  }

  static inline flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>>> ConvertFbs(flatbuffers::FlatBufferBuilder& builder, const TransformOperations& transforms, float density, float w, float h) {
    auto transformOperations = transforms.Operations();
    auto transformCount = transformOperations.size();

    if (transformCount == 0) {
      //No transform
      return 0;
    } else {
      std::vector<flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>> result;
      for (const auto &pOperation : transformOperations) {
        const TransformOperation &operation = *pOperation;
        flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop> prop;
        switch (operation.GetType()) {
          case TransformOperation::kScaleX: {
            const auto &scale = To<ScaleTransformOperation>(operation);
            auto key = builder.CreateString("scaleX");
            auto float_value = CreateFloatValue(builder, scale.X());
            prop = CreateProp(builder, key, Value_FloatValue, float_value.Union());
            break;
          }
          case TransformOperation::kScaleY:{
            const auto &scale = To<ScaleTransformOperation>(operation);
            auto key = builder.CreateString("scaleY");
            auto float_value = CreateFloatValue(builder, scale.Y());
            prop = CreateProp(builder, key, Value_FloatValue, float_value.Union());
            break;
          }
          case TransformOperation::kScaleZ:{
            const auto &scale = To<ScaleTransformOperation>(operation);
            auto key = builder.CreateString("scaleZ");
            auto float_value = CreateFloatValue(builder, scale.Z());
            prop = CreateProp(builder, key, Value_FloatValue, float_value.Union());
            break;
          }
          case TransformOperation::kScale:{
            const auto& scale = To<ScaleTransformOperation>(operation);
            std::vector<float> floats = {(float) scale.X(), (float) scale.Y()};
            auto float_array = CreateFloatArrayDirect(builder, &floats); // vals = [1.1, 2.2, 3.3]
            auto key = builder.CreateString("scale");
            prop = CreateProp(builder, key, Value_FloatArray, float_array.Union());
            break;
          }
          case TransformOperation::kTranslateX: {
            const auto& translate = To<TranslateTransformOperation>(operation);
            auto key = builder.CreateString("translateX");
            auto float_value = CreateFloatValue(builder, mt::StyleConvertor::ConvertLengthToFloat(translate.X(), density, w));
            prop = CreateProp(builder, key, Value_FloatValue, float_value.Union());
            break;
          }
          case TransformOperation::kTranslateY: {
            const auto& translate = To<TranslateTransformOperation>(operation);
            auto key = builder.CreateString("translateY");
            auto float_value = CreateFloatValue(builder, mt::StyleConvertor::ConvertLengthToFloat(translate.Y(), density, h));
            prop = CreateProp(builder, key, Value_FloatValue, float_value.Union());
            break;
          }
          case TransformOperation::kTranslateZ: {
            const auto& translate = To<TranslateTransformOperation>(operation);
            auto key = builder.CreateString("translateZ");
            auto float_value = CreateFloatValue(builder, translate.Z());
            prop = CreateProp(builder, key, Value_FloatValue, float_value.Union());
            break;
          }
          case TransformOperation::kTranslate: {
            const auto& translate = To<TranslateTransformOperation>(operation);
            std::vector<float> floats = {mt::StyleConvertor::ConvertLengthToFloat(translate.X(), density, w), mt::StyleConvertor::ConvertLengthToFloat(translate.Y(), density, h)};
            auto key = builder.CreateString("translate");
            auto float_array = CreateFloatArrayDirect(builder, &floats);
            prop = CreateProp(builder, key, Value_FloatArray, float_array.Union());
            break;
          }
          case TransformOperation::kRotateX: {
            const auto &rotate = To<RotateTransformOperation>(operation);
            auto key = builder.CreateString("rotateX");
            auto float_value = CreateFloatValue(builder, rotate.Angle() * M_PI / 180);
            prop = CreateProp(builder, key, Value_FloatValue, float_value.Union());
            break;
          }
          case TransformOperation::kRotateY: {
            const auto &rotate = To<RotateTransformOperation>(operation);
            auto key = builder.CreateString("rotateY");
            auto float_value = CreateFloatValue(builder, rotate.Angle() * M_PI / 180);
            prop = CreateProp(builder, key, Value_FloatValue, float_value.Union());
            break;
          }
          case TransformOperation::kRotateZ: {
            const auto &rotate = To<RotateTransformOperation>(operation);
            auto key = builder.CreateString("rotateZ");
            auto float_value = CreateFloatValue(builder, rotate.Angle() * M_PI / 180);
            prop = CreateProp(builder, key, Value_FloatValue, float_value.Union());
            break;
          }
          case TransformOperation::kSkewX: {
            const auto& skew = To<SkewTransformOperation>(operation);
            auto key = builder.CreateString("skewX");
            auto float_value = CreateFloatValue(builder, skew.AngleX());
            prop = CreateProp(builder, key, Value_FloatValue, float_value.Union());
            break;
          }
          case TransformOperation::kSkewY: {
            const auto& skew = To<SkewTransformOperation>(operation);
            auto key = builder.CreateString("skewY");
            auto float_value = CreateFloatValue(builder, skew.AngleY());
            prop = CreateProp(builder, key, Value_FloatValue, float_value.Union());
            break;
          }
          case TransformOperation::kSkew: {
            const auto& skew = To<SkewTransformOperation>(operation);
            auto key = builder.CreateString("skew");
            std::vector<float> floats = {(float) skew.AngleX(), (float) skew.AngleY()};
            auto float_array = CreateFloatArrayDirect(builder, &floats);
            prop = CreateProp(builder, key, Value_FloatArray, float_array.Union());
            break;
          }
          case TransformOperation::kPerspective: {
            const auto& perspective = To<PerspectiveTransformOperation>(operation);
            auto key = builder.CreateString("perspective");
            auto float_value = CreateFloatValue(builder, perspective.Perspective().value());
            prop = CreateProp(builder, key, Value_FloatValue, float_value.Union());
            break;
          }
          case TransformOperation::kMatrix: {
            const auto& matrix = To<MatrixTransformOperation>(operation).Matrix();
            std::vector<float> floats = {static_cast<float>(matrix.rc(0, 0)),
                                         static_cast<float>(matrix.rc(0, 1)),
                                         static_cast<float>(matrix.rc(1, 0)),
                                         static_cast<float>(matrix.rc(1, 1)),
                                         static_cast<float>(matrix.rc(3, 0)),
                                         static_cast<float>(matrix.rc(3, 0))};
            auto key = builder.CreateString("matrix");
            auto float_array = CreateFloatArrayDirect(builder, &floats);
            prop = CreateProp(builder, key, Value_FloatArray, float_array.Union());
            break;
          }
          case TransformOperation::kMatrix3D: {
            const auto& matrix = To<Matrix3DTransformOperation>(operation).Matrix();
            std::vector<float> floats = {static_cast<float>(matrix.rc(0, 0)),
                                         static_cast<float>(matrix.rc(0, 1)),
                                         static_cast<float>(matrix.rc(0, 2)),
                                         static_cast<float>(matrix.rc(0, 3)),
                                         static_cast<float>(matrix.rc(1, 0)),
                                         static_cast<float>(matrix.rc(1, 1)),
                                         static_cast<float>(matrix.rc(1, 2)),
                                         static_cast<float>(matrix.rc(1, 3)),
                                         static_cast<float>(matrix.rc(2, 0)),
                                         static_cast<float>(matrix.rc(2, 1)),
                                         static_cast<float>(matrix.rc(2, 2)),
                                         static_cast<float>(matrix.rc(2, 3)),
                                         static_cast<float>(matrix.rc(3, 0)),
                                         static_cast<float>(matrix.rc(3, 1)),
                                         static_cast<float>(matrix.rc(3, 2)),
                                         static_cast<float>(matrix.rc(3, 3))};
            auto key = builder.CreateString("matrix3d");
            auto float_array = CreateFloatArrayDirect(builder, &floats);
            prop = CreateProp(builder, key, Value_FloatArray, float_array.Union());
            break;
          }
        }
        result.push_back(prop);
      }
      return builder.CreateVector(result);
    }
  }

  static inline flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<Prop>>> ConvertFbs(flatbuffers::FlatBufferBuilder& builder, const NativeTransforms& transforms, float density) {
    std::vector<flatbuffers::Offset<Prop>> result;
    for (auto transform: *transforms) {
      NativeTransform::Type type = transform->type;
      flatbuffers::Offset<Prop> prop;
      switch (type) {
        case NativeTransform::Type::Translate: {
          auto translate = static_cast<TranslateTransform *>(transform.get());
          std::vector<float> floats = {translate->x * density, translate->y * density};
          auto key = builder.CreateString("translate");
          auto float_array = CreateFloatArrayDirect(builder, &floats);
          prop = CreateProp(builder, key, Value_FloatArray, float_array.Union());
          break;
        }
        case NativeTransform::Type::Scale: {
          auto scale = static_cast<ScaleTransform *>(transform.get());
          std::vector<float> floats = {scale->x, scale->y};
          auto float_array = CreateFloatArrayDirect(builder, &floats);
          auto key = builder.CreateString("scale");
          prop = CreateProp(builder, key, Value_FloatArray, float_array.Union());
          break;
        }
        case NativeTransform::Type::Rotate: {
          auto rotate = static_cast<RotateTransform *>(transform.get());
          auto key = builder.CreateString("rotateZ");
          auto float_value = CreateFloatValue(builder, rotate->angle * M_PI / 180);
          prop = CreateProp(builder, key, Value_FloatValue, float_value.Union());
          break;
        }
        case NativeTransform::Type::Skew: {
          auto skew = static_cast<SkewTransform *>(transform.get());
          auto key = builder.CreateString("skew");
          std::vector<float> floats = {(float) skew->angle_x, (float) skew->angle_y};
          auto float_array = CreateFloatArrayDirect(builder, &floats);
          prop = CreateProp(builder, key, Value_FloatArray, float_array.Union());
          break;
        }
        case NativeTransform::Type::Matrix: {
          auto matrix = static_cast<MatrixTansform *>(transform.get());
          std::vector<float> floats = {matrix->value00, matrix->value01, matrix->value10, matrix->value11, matrix->value30, matrix->value31};
          auto key = builder.CreateString("matrix");
          auto float_array = CreateFloatArrayDirect(builder, &floats);
          prop = CreateProp(builder, key, Value_FloatArray, float_array.Union());
          break;
        }
        case NativeTransform::Type::Matrix3D: {
          auto matrix = static_cast<Matrix3DTansform *>(transform.get());
          std::vector<float> floats = {matrix->value00, matrix->value01, matrix->value02, matrix->value03,
                                       matrix->value10, matrix->value11, matrix->value12, matrix->value13,
                                       matrix->value20, matrix->value21, matrix->value22, matrix->value23,
                                       matrix->value30, matrix->value31, matrix->value32, matrix->value33};
          auto key = builder.CreateString("matrix3d");
          auto float_array = CreateFloatArrayDirect(builder, &floats);
          prop = CreateProp(builder, key, Value_FloatArray, float_array.Union());
          break;
        }
        case NativeTransform::Type::Perspective: {
          auto perspective = static_cast<PerspectiveTransform *>(transform.get());
          auto key = builder.CreateString("perspective");
          auto float_value = CreateFloatValue(builder, perspective->value);
          prop = CreateProp(builder, key, Value_FloatValue, float_value.Union());
          break;
        }
      }
      result.push_back(prop);
    }
    if (result.size() == 0) {
      return 0;
    }
    return builder.CreateVector(result);
  }

#endif
};

}; // namespace blink::mt
