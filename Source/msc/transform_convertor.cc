//
//  transform_convertor.cc
//  MSCRenderer
//
//  Created by <PERSON><PERSON> on 2025/4/16.
//

#include "transform_convertor.h"

#include "device.h"

#include "third_party/blink/renderer/platform/geometry/layout_unit.h"
#include "third_party/blink/renderer/platform/transforms/matrix_3d_transform_operation.h"
#include "third_party/blink/renderer/platform/transforms/matrix_transform_operation.h"
#include "third_party/blink/renderer/platform/transforms/perspective_transform_operation.h"
#include "third_party/blink/renderer/platform/transforms/skew_transform_operation.h"

namespace blink::mt {

namespace {

static inline float ConvertLengthToFloat(const Length& length, const LayoutUnit& ref_length) {
  switch (length.GetType()) {
    case Length::Type::kFixed:
      return length.Pixels();
    case Length::Type::kPercent:
      return length.Percent() / 100.0 * ref_length.ToFloat();
    default:
      return 0;
  }
}

};

NativeTransforms ConvertTransform(const TransformOperations& transform_ops, const RenderNode::Rect& rect) {
  if (transform_ops.size() == 0) {
    return nullptr;
  }

  auto transforms = std::make_shared<std::vector<std::shared_ptr<NativeTransform>>>();
  for (const auto& operation : transform_ops.Operations()) {
    const TransformOperation &op = *operation;
    switch (op.GetType()) {

    case TransformOperation::kTranslateX:
    case TransformOperation::kTranslateY:
    case TransformOperation::kTranslateZ:
    case TransformOperation::kTranslate:
    case TransformOperation::kTranslate3D: {
      auto translate = std::make_shared<TranslateTransform>();
      const auto& translate_op = To<TranslateTransformOperation>(op);
      if (op.GetType() == TransformOperation::kTranslateX) {
        translate->x = Device::gridAlignedValue(ConvertLengthToFloat(translate_op.X(), rect.width));
      } else if (op.GetType() == TransformOperation::kTranslateY) {
        translate->y = Device::gridAlignedValue(ConvertLengthToFloat(translate_op.Y(), rect.height));
      } else if (op.GetType() == TransformOperation::kTranslateZ) {
        translate->z = translate_op.Z();
      } else if (op.GetType() == TransformOperation::kTranslate || op.GetType() == TransformOperation::kTranslate3D) {
        translate->x = Device::gridAlignedValue(ConvertLengthToFloat(translate_op.X(), rect.width));
        translate->y = Device::gridAlignedValue(ConvertLengthToFloat(translate_op.Y(), rect.height));
        translate->z = translate_op.Z();
      }
      transforms->push_back(translate);
      break;
    }

    case TransformOperation::kScaleX:
    case TransformOperation::kScaleY:
    case TransformOperation::kScaleZ:
    case TransformOperation::kScale:
    case TransformOperation::kScale3D: {
      auto scale = std::make_shared<ScaleTransform>();
      const auto& scale_op = To<ScaleTransformOperation>(op);
      scale->x = scale_op.X();
      scale->y = scale_op.Y();
      scale->z = scale_op.Z();
      transforms->push_back(scale);
      break;
    }

    case TransformOperation::kRotateX:
    case TransformOperation::kRotateY:
    case TransformOperation::kRotateZ:
    case TransformOperation::kRotate:
    case TransformOperation::kRotate3D: {
      auto rotate = std::make_shared<RotateTransform>();
      const auto& rotate_op = To<RotateTransformOperation>(op);
      rotate->angle = rotate_op.Angle() * M_PI / 180;
      rotate->x = rotate_op.X();
      rotate->y = rotate_op.Y();
      rotate->z = rotate_op.Z();
      transforms->push_back(rotate);
      break;
    }

    case TransformOperation::kSkewX:
    case TransformOperation::kSkewY:
    case TransformOperation::kSkew: {
      auto skew = std::make_shared<SkewTransform>();
      const auto& skew_op = To<SkewTransformOperation>(op);
      skew->angle_x = skew_op.AngleX() * M_PI / 180;
      skew->angle_y = skew_op.AngleY() * M_PI / 180;
      transforms->push_back(skew);
      break;
    }

    case TransformOperation::kPerspective: {
      auto perspective = std::make_shared<PerspectiveTransform>();
      const auto& perspective_op = To<PerspectiveTransformOperation>(op);
      if (perspective_op.Perspective().has_value()) {
        perspective->value = perspective_op.Perspective().value();
        transforms->push_back(perspective);
      }
      break;
    }

    case TransformOperation::kMatrix: {
      auto matrix = std::make_shared<MatrixTansform>();
      const auto& matrix_op = To<MatrixTransformOperation>(op);
      const auto& transform = matrix_op.Matrix();
      matrix->value00 = transform.rc(0, 0);
      matrix->value01 = transform.rc(0, 1);
      matrix->value10 = transform.rc(1, 0);
      matrix->value11 = transform.rc(1, 1);
      matrix->value30 = transform.rc(3, 0);
      matrix->value31 = transform.rc(3, 1);
      transforms->push_back(matrix);
      break;
    }

    case TransformOperation::kMatrix3D: {
      auto matrix = std::make_shared<Matrix3DTansform>();
      const auto& matrix_op = To<Matrix3DTransformOperation>(op);
      const auto& transform = matrix_op.Matrix();
      matrix->value00 = transform.rc(0, 0);
      matrix->value01 = transform.rc(0, 1);
      matrix->value02 = transform.rc(0, 2);
      matrix->value03 = transform.rc(0, 3);
      matrix->value10 = transform.rc(1, 0);
      matrix->value11 = transform.rc(1, 1);
      matrix->value12 = transform.rc(1, 2);
      matrix->value13 = transform.rc(1, 3);
      matrix->value20 = transform.rc(2, 0);
      matrix->value21 = transform.rc(2, 1);
      matrix->value22 = transform.rc(2, 2);
      matrix->value23 = transform.rc(2, 3);
      matrix->value30 = transform.rc(3, 0);
      matrix->value31 = transform.rc(3, 1);
      matrix->value32 = transform.rc(3, 2);
      matrix->value33 = transform.rc(3, 3);
      transforms->push_back(matrix);
      break;
    }

    default:
      continue;
    }
  }

  return transforms->size() > 0 ? transforms : nullptr;
}

}; // namespace blink::mt
