//
//  message_proxy.cpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/2/14.
//

#include "message_proxy.h"
#include "mtdocument.h"
#include "../../logger.h"
#include "TraceUtils.h"
#include "text_node.h"

namespace msc {
namespace native_dom {

MessageProxy::MessageProxy(std::shared_ptr<msc::native_dom::DocumentRegistry> documentRegistry)
: document_registry_(documentRegistry) {
}

MessageProxy::~MessageProxy() {
  document_registry_ = nullptr;
}

std::shared_ptr<blink::mt::MTDocument> MessageProxy::GetDocument(int pageId) {
    return document_registry_->getDocument(pageId);
}

void MessageProxy::SendCreateViewMessage(int pageId, JSIBasicElement &element) {
    auto mtDocument = GetDocument(pageId);
    if (!mtDocument) {
        MSC_RENDERER_LOG_ERROR("[native_dom] MessageProxy::Send<PERSON>reateViewMessage cannot find blink document for page %d\n", pageId);
        return;
    }
    auto nodeId = element.GetNodeId();
    blink::mt::PropsBuilder props_builder;
    element.CopyPropsToPropsBuilder(props_builder);

#ifndef __APPLE__
    if (element.nodeTag() == Tag::MSC_TEXT) {
        props_builder.setProp("documentKey", std::to_string((long long)(mtDocument.get())));
    }
#endif

    mtDocument->createNode(nodeId, element.tagName(), 1, props_builder.getProps());
    MSC_RENDERER_LOG_DEBUG("[native_dom] MessageProxy::SendCreateViewMessage %d, tagName: %s, pageId: %d, props: %s\n", element.GetNodeId(), element.tagName(), pageId, props_builder.getProps()->GetDescription().c_str());
}

void MessageProxy::SendCreateTextNodeMessage(int pageId, const TextNode &text_node) {
    auto node_id = text_node.GetNodeId();
    MSC_RENDERER_LOG_DEBUG("[native_dom] MessageProxy::sendCreateTextNodeMessage %d, pageId: %d\n", node_id, pageId);
    auto mtDocument = GetDocument(pageId);
    if (!mtDocument) {
        MSC_RENDERER_LOG_ERROR("[native_dom] MessageProxy::SendCreateTextNodeMessage cannot find blink document for page %d\n", pageId);
        return;
    }
    blink::mt::PropsBuilder props_builder;
    props_builder.setProp("text", text_node.TextContent());
    mtDocument->createNode(node_id, "MSCRawText", 1, props_builder.getProps());
}

void MessageProxy::SendUpdateViewMessage(int pageId, Element &element, const MSCString &attribute_name, const blink::mt::PropValue &attribute_value) {
    MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(MessageProxy::SendUpdateViewMessage);
    int element_id = element.GetNodeId();
    MSC_RENDERER_LOG_DEBUG("[native_dom] MessageProxy::SendUpdateViewMessage %d, viewId: %d, (%s, %s)\n", pageId, element_id, attribute_name.c_str(), attribute_value.stringValue().c_str());
    auto document = GetDocument(pageId);
    if (!document) {
        return;
    }
    blink::mt::PropsBuilder props_builder;
    props_builder.setProp(attribute_name, attribute_value);
  
#ifndef __APPLE__
    if (element.nodeTag() == Tag::MSC_TEXT) {
        props_builder.setProp("documentKey", std::to_string((long long)(document.get())));
    }
#endif

    document->updateNode(element_id, element.tagName(), props_builder.getProps());
}

void MessageProxy::SendRemoveViewMessage(int pageId, int viewId) {
    MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(MessageProxy::SendRemoveViewMessage);
    MSC_RENDERER_LOG_DEBUG("[native_dom] MessageProxy::SendRemoveViewMessage %d, viewId: %d\n", pageId, viewId);
    auto document = GetDocument(pageId);
    if (!document) {
        MSC_RENDERER_LOG_ERROR("[native_dom] MessageProxy::SendRemoveViewMessage cannot find blink document for page %d\n", pageId);
        return;
    }
    document->removeNode(viewId);
}

void MessageProxy::SendQueryEnhancedMessage(
    int pageId, blink::mt::QueryEnhancedParams &&params,
    JSCallbackInfo callback_info) {
  MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(MessageProxy::SendQueryEnhancedMessage);
  MSC_RENDERER_LOG_DEBUG(
      "[native_dom] MessageProxy::SendQueryEnhancedMessage %d, "
      "params.is_viewport: %d\n",
      pageId, params.is_viewport);
  auto mt_document = GetDocument(pageId);
  if (mt_document) {
    mt_document->QueryEnhanced(std::move(params), callback_info);
  }
}

void MessageProxy::SendCreateIntersectionObserverMessage(
    int page_id, blink::mt::CreateIntersectionObserverParams &&params,
    JSCallbackInfo callback_info) {
  MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(
      MessageProxy::SendManageIntersectionObserverMessage);
  MSC_RENDERER_LOG_DEBUG(
      "[native_dom] MessageProxy::SendManageIntersectionObserverMessage %d, "
      "id: %d, root_id: %d\n",
      page_id, params.intersection_observer_id_, params.root_tag_);
  auto mt_document = GetDocument(page_id);
  if (mt_document) {
    mt_document->CreateIntersectionObserver(std::move(params), callback_info);
  }
}

void MessageProxy::SendIntersectionObserverObserveMessage(
    int page_id, int intersection_observer_id, int target_id) {
  MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(
      MessageProxy::SendIntersectionObserverObserveMessage);
  MSC_RENDERER_LOG_DEBUG(
      "[native_dom] MessageProxy::SendIntersectionObserverObserveMessage %d, "
      "id: %d, target_id: %d\n",
      page_id, intersection_observer_id, target_id);
  auto mt_document = GetDocument(page_id);
  if (mt_document) {
    mt_document->IntersectionObserverObserve(intersection_observer_id, target_id);
  }
}

void MessageProxy::SendAppendChildMessage(int pageId, int parentId, const std::shared_ptr<const std::vector<int>>& child_tags) {
    MSC_RENDERER_LOG_DEBUG("[native_dom] MessageProxy::SendAppendChildMessage %d, children: %s\n",
                           parentId,
                           MSCConvertToString(*child_tags).c_str());
    auto mtDocument = GetDocument(pageId);
    if (!mtDocument) {
        MSC_RENDERER_LOG_ERROR("[native_dom] MessageProxy::SendAppendChildMessage cannot find blink document for page %d\n", pageId);
        return;
    }
    mtDocument->setChildren(parentId, child_tags);
}

void MessageProxy::SendManageChildrenMessage(int pageId, int parentId, const std::shared_ptr<const blink::mt::MTDocument::ChildrenChanges>& children_changes) {
    MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(MessageProxy::SendManageChildrenMessage);
    MSC_RENDERER_LOG_DEBUG("[native_dom] MessageProxy::SendManageChildrenMessage %d, added_tags: %s, added_indices, %s \n", parentId,
                           MSCConvertToString(children_changes->add_child_msc_tags).c_str(), MSCConvertToString(children_changes->add_at_indices).c_str());
    auto mtDocument = GetDocument(pageId);
    if (!mtDocument) {
        MSC_RENDERER_LOG_ERROR("[native_dom] MessageProxy::SendManageChildrenMessage cannot find blink document for page %d\n", pageId);
        return;
    }
    mtDocument->manageChildren(parentId, children_changes);
}

void MessageProxy::SendBDCMessage(int pageId, blink::mt::LayoutReason layoutReason) {
  MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(MessageProxy::SendBDCMessage);
  MSC_RENDERER_LOG_DEBUG("[native_dom] MessageProxy::SendBDCMessage %d\n", pageId);
  auto mtDocument = GetDocument(pageId);
  if (!mtDocument) {
      MSC_RENDERER_LOG_ERROR("[native_dom] MessageProxy::SendBDCMessage cannot find blink document for page %d\n", pageId);
    return;
  }
  mtDocument->layoutRoot(layoutReason);
}

void MessageProxy::SendCreateKeyframesAnimationEnhancedMessage(
    int page_id,
    const std::shared_ptr<blink::mt::AnimationProperties> &properties,
    JSCallbackInfo callback_info) {
  MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(
      MessageProxy::SendCreateKeyframesAnimationEnhancedMessage);
  MSC_RENDERER_LOG_DEBUG(
      "[native_dom] MessageProxy::SendCreateKeyframesAnimationEnhancedMessage "
      "%d\n",
      page_id);
  auto mtDocument = GetDocument(page_id);
  if (!mtDocument) {
    MSC_RENDERER_LOG_ERROR(
        "[native_dom] "
        "MessageProxy::SendCreateKeyframesAnimationEnhancedMessage cannot find "
        "blink document for page %d\n",
        page_id);
    return;
  }

  mtDocument->CreateKeyframesAnimationEnhanced(properties, callback_info);
}

void MessageProxy::SendClearKeyframesAnimationEnhancedMessage(
    int page_id,
    const std::shared_ptr<blink::mt::ClearAnimationProperties> &options,
    JSCallbackInfo callback_info) {
  MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(
      MessageProxy::SendClearKeyframesAnimationEnhancedMessage);
  MSC_RENDERER_LOG_DEBUG(
      "[native_dom] MessageProxy::SendClearKeyframesAnimationEnhancedMessage "
      "%d\n",
      page_id);
  auto mtDocument = GetDocument(page_id);
  if (!mtDocument) {
    MSC_RENDERER_LOG_ERROR(
        "[native_dom] MessageProxy::SendClearKeyframesAnimationEnhancedMessage "
        "cannot find blink document for page %d\n",
        page_id);
    return;
  }

  mtDocument->ClearKeyframesAnimationEnhanced(options, callback_info);
}

}  // namespace native_dom
}  // namespace msc
