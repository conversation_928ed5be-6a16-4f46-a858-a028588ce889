//
//  display_item_helper_android.cpp
//  MSCRenderer
//
//  Created by Admin on 2025/2/12.
//

#ifndef __APPLE__

#include "third_party/blink/renderer/core/html/html_element.h"
#include "third_party/blink/renderer/core/layout/layout_box.h"
#include "third_party/blink/renderer/core/css/properties/longhands.h"

#include "types_def.h"
#include "style_convertor.h"
#include "device.h"
#include <flatbuffers/flatbuffers.h>
#include <fstream>
#include "Command_generated.h"
#include "NativeLog.h"

using namespace com::meituan::android::msc::renderer::generated;

namespace blink::mt {

std::shared_ptr<const NativeStyle> ConvertStyle(const LayoutBox* box){
  auto element = DynamicTo<HTMLElement>(box->GetNode());
  auto *style = box->Style();
  auto native_style = std::make_shared<NativeStyle>();
  if (element) {
    mt::PropValue value = element->getProp("isMsi");
    native_style->is_msi = !value.isNull();
  }
  native_style->hidden = style->Visibility() == EVisibility::kHidden;
  native_style->overflow_x = style->OverflowX();
  native_style->overflow_y = style->OverflowY();
  native_style->background_color = style->VisitedDependentColor(GetCSSPropertyBackgroundColor());
  native_style->color = style->GetCurrentColor();
  native_style->default_color = StyleColor(Color::kBlack).Resolve(blink::Color(), style->UsedColorScheme());

  native_style->background_image = style->BackgroundImage().Utf8();
  native_style->background_gradient = style->BackgroundData().GetGradient();
  native_style->background_size = style->BackgroundData().Size();

  auto backgroundRepeat = style->BackgroundData().GetRepeat();
  native_style->background_repeat_x = backgroundRepeat.x;
  native_style->background_repeat_y = backgroundRepeat.y;

  native_style->border_top_style = style->BorderTopStyle();
  native_style->border_bottom_style = style->BorderBottomStyle();
  native_style->border_left_style = style->BorderLeftStyle();
  native_style->border_right_style = style->BorderRightStyle();

  native_style->border_top_color = style->BorderTopColor().Resolve(style->GetCurrentColor(), style->UsedColorScheme());
  native_style->border_bottom_color = style->BorderBottomColor().Resolve(style->GetCurrentColor(), style->UsedColorScheme());
  native_style->border_left_color = style->BorderLeftColor().Resolve(style->GetCurrentColor(), style->UsedColorScheme());
  native_style->border_right_color = style->BorderRightColor().Resolve(style->GetCurrentColor(), style->UsedColorScheme());
  native_style->default_border_color = StyleColor::CurrentColor().Resolve(style->GetCurrentColor(), style->UsedColorScheme());

  native_style->border_top_width = Device::gridAlignedValue(style->BorderTopWidth(), true);
  native_style->border_bottom_width = Device::gridAlignedValue(style->BorderBottomWidth(), true);
  native_style->border_left_width = Device::gridAlignedValue(style->BorderLeftWidth(), true);
  native_style->border_right_width = Device::gridAlignedValue(style->BorderRightWidth(), true);

  native_style->border_top_left_radius = style->BorderTopLeftRadius();
  native_style->border_top_right_radius = style->BorderTopRightRadius();
  native_style->border_bottom_left_radius = style->BorderBottomLeftRadius();
  native_style->border_bottom_right_radius = style->BorderBottomRightRadius();

  native_style->opacity = style->Opacity();

  native_style->font_size = style->FontSize();
  native_style->font_style = style->GetFontDescription().Style() != kNormalSlopeValue ? "italic" : "normal";
  native_style->text_align = style->GetTextAlign();

  native_style->transform = style->Transform();
  native_style->box_shadow = style->BoxShadow();
  if (native_style->box_shadow != nullptr && native_style->box_shadow->Shadows().size() > 0) {
      wtf_size_t shadowCount = native_style->box_shadow->Shadows().size();
      for (wtf_size_t i = 0; i < shadowCount; ++i) {
          auto shadow = native_style->box_shadow->Shadows()[i];
        native_style->shadow_color_list.push_back(shadow.GetColor().Resolve(style->GetCurrentColor(), style->UsedColorScheme()));
      }
  }
  native_style->pointer_events = style->UsedPointerEvents();
  return native_style;
}

}

#endif
